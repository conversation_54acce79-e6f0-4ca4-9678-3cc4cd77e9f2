import yaml
from template_manager import template_manager
from prompt_saver import save_prompts_quiet

# 动态导入 API 库
try:
    from volcenginesdkarkruntime import Ark
    from volcenginesdkarkruntime.types.chat import ChatCompletionStreamOptionsParam
    ARK_AVAILABLE = True
except ImportError:
    print("警告: volcenginesdkarkruntime 库未安装，无法使用 ARK API")
    ARK_AVAILABLE = False

try:
    from openai import OpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    print("警告: openai 库未安装，无法使用 OpenAI API")
    OPENAI_AVAILABLE = False

def load_config(config_path="config.yaml"):
    """加载配置文件，根据setting_api_type选择对应的API配置"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
            
            # 获取设定生成API类型选择，默认为ark
            api_type = config.get('setting_api_type', 'ark')
            
            if api_type == 'ark':
                if 'api' in config:
                    config['api_type'] = 'ark'
                else:
                    raise ValueError("配置文件中未找到 api 配置")
            elif api_type == 'openai':
                if 'openaiapi' in config:
                    config['api'] = config['openaiapi']
                    config['api_type'] = 'openai'
                else:
                    raise ValueError("配置文件中未找到 openaiapi 配置")
            else:
                raise ValueError(f"不支持的API类型: {api_type}，仅支持 'ark' 或 'openai'")
            
            return config
    except FileNotFoundError:
        print(f"错误: 配置文件 {config_path} 未找到")
        raise
    except Exception as e:
        print(f"配置文件加载失败: {e}")
        raise

def load_application_config():
    """加载应用程序配置文件"""
    try:
        with open("application.yaml", 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    except FileNotFoundError:
        print("警告: application.yaml 文件未找到")
        return {}
    except Exception as e:
        print(f"警告: application.yaml 文件加载失败 ({e})")
        return {}

def create_client():
    """根据配置创建对应的API客户端"""
    api_type = CONFIG.get('api_type', 'ark')
    
    if api_type == 'ark':
        if not ARK_AVAILABLE:
            raise ImportError("ARK API 库未安装，请安装 volcenginesdkarkruntime")
        return Ark(
            api_key=CONFIG['api']['key'],
            timeout=CONFIG['api']['timeout']
        )
    elif api_type == 'openai':
        if not OPENAI_AVAILABLE:
            raise ImportError("OpenAI 库未安装，请安装 openai")
        return OpenAI(
            api_key=CONFIG['api']['key'],
            base_url=CONFIG['api']['base_url'],
            timeout=CONFIG['api']['timeout']
        )
    else:
        raise ValueError(f"不支持的API类型: {api_type}")

def get_max_tokens():
    """获取当前API类型对应的max_tokens值"""
    api_type = CONFIG.get('api_type', 'ark')
    return CONFIG.get('token_limits', {}).get(api_type, 12288)

# 全局配置
CONFIG = load_config()

# 显示当前使用的API类型
api_type = CONFIG.get('api_type', 'ark')
max_tokens = get_max_tokens()
print(f"[INFO] 小说设定生成API类型: {api_type.upper()}")
print(f"[INFO] 设定生成模型: {CONFIG['api']['model']}")
print(f"[INFO] 设定生成Max Tokens: {max_tokens}")
if CONFIG.get('api_type') == 'openai':
    print(f"[INFO] 设定生成基础URL: {CONFIG['api']['base_url']}")
print()

def remove_protagonist2_from_template(template_content: str) -> str:
    """
    使用正则表达式删除模板中所有PROTAGONIST2相关内容
    """
    import re
    
    # 删除PROTAGONIST2的整个人物设定块（从标题行到下一个标题行之前）
    template_content = re.sub(r'\*\*\{\{PROTAGONIST2_NAME\}\}.*?\*\*.*?(?=\n\*\*[^{]|\n### |\n## |\Z)', '', template_content, flags=re.DOTALL)
    
    # 删除剩余的单独PROTAGONIST2变量行
    template_content = re.sub(r'^.*\{\{PROTAGONIST2_[^}]+\}\}.*$\n?', '', template_content, flags=re.MULTILINE)
    
    # 删除章节大纲中提到PROTAGONIST2_NAME的部分
    # 删除包含PROTAGONIST2_NAME的句子片段
    template_content = re.sub(r'[^。]*\{\{PROTAGONIST2_NAME\}\}[^。]*', '', template_content)
    
    # 删除孤立的"与"、"或"、"等"等连接词
    template_content = re.sub(r'、\s*与\s*[、。]', '。', template_content)
    template_content = re.sub(r'与\s*等[^。]*', '', template_content)
    template_content = re.sub(r'、\s*或\s*[、。]', '。', template_content)
    
    return template_content

def extract_pure_template(template_content):
    """从story_rules.md中提取纯净的模板部分"""
    lines = template_content.split('\n')
    template_start = False
    pure_template = []
    
    for line in lines:
        # 从输出模板结构开始提取
        if '### 输出模板结构' in line:
            template_start = True
            continue  # 跳过这个标题行
        elif template_start:
            pure_template.append(line)
    
    return '\n'.join(pure_template)

def get_writing_element_info(writing_element_key):
    """根据写作元素key获取详细信息"""
    if not writing_element_key:
        return None
    
    try:
        app_config = load_application_config()
        if 'writing_elements' not in app_config:
            return None
        
        # 在所有分类中查找该元素
        for category_key, category_data in app_config['writing_elements'].items():
            elements = category_data.get('elements', {})
            if writing_element_key in elements:
                element_data = elements[writing_element_key]
                return {
                    'name': element_data.get('name', writing_element_key),
                    'description': element_data.get('description', ''),
                    'category': category_data.get('category', category_key),
                    'bind': element_data.get('bind', '')
                }
        
        return None
    except Exception as e:
        print(f"获取写作元素信息失败: {e}")
        return None

def generate_story_setting(user_prompt, novel_genre=None, writing_element=None, protagonists=None, template_choice=None):
    """生成小说设定"""
    client = create_client()
    
    # 读取人类化模块
    humanization_content = ""
    try:
        with open(CONFIG['files']['humanization_module'], 'r', encoding='utf-8') as f:
            humanization_content = f.read()
    except FileNotFoundError:
        print("警告: 人类化模块文件未找到，将使用默认设置")
    
    
    # 根据选择确定模板文件
    template_file_map = {
        "12章": "story_rules.md",
        "24章": "story_rules_24.md", 
        "36章": "story_rules_36.md"
    }
    
    if template_choice and template_choice in template_file_map:
        template_file = template_file_map[template_choice]
        print(f"[INFO] 使用模板: {template_choice} ({template_file})")
    else:
        # 当 template_choice = None 时，默认使用24章模板
        if template_choice is None:
            template_file = template_file_map["24章"]
            print(f"[INFO] template_choice=None，使用默认24章模板: ({template_file})")
        else:
            # 使用配置文件中的模板
            template_file = CONFIG['files']['story_template']
            print(f"[INFO] 使用配置文件中的默认模板: {template_file}")
    
    # 读取小说模板规则
    with open(template_file, 'r', encoding='utf-8') as f:
        template_content = f.read()
    
    # 先提取纯净模板（保持变量格式不变）
    template_content = extract_pure_template(template_content)
    
    # 处理主角信息的动态模板替换
    if protagonists and len(protagonists) > 0:
        # 主角1信息
        if len(protagonists) >= 1:
            protag1 = protagonists[0]
            template_content = template_content.replace('{{PROTAGONIST1_NAME}}', protag1.get('name', '[主角姓名]'))
            template_content = template_content.replace('{{PROTAGONIST1_GENDER}}', protag1.get('gender', '[性别]'))
        else:
            # 如果没有主角1信息，保持占位符
            template_content = template_content.replace('{{PROTAGONIST1_NAME}}', '[主角姓名]')
            template_content = template_content.replace('{{PROTAGONIST1_GENDER}}', '[性别]')
        
        # 主角2信息
        if len(protagonists) >= 2:
            protag2 = protagonists[1]
            template_content = template_content.replace('{{PROTAGONIST2_NAME}}', protag2.get('name', '[配角姓名]'))
            template_content = template_content.replace('{{PROTAGONIST2_GENDER}}', protag2.get('gender', '[性别]'))
        else:
            # 如果没有主角2信息，删除模板中所有PROTAGONIST2相关内容
            template_content = remove_protagonist2_from_template(template_content)
    else:
        # 如果没有主角信息，保持原始占位符
        template_content = template_content.replace('{{PROTAGONIST1_NAME}}', '[主角姓名]')
        template_content = template_content.replace('{{PROTAGONIST1_GENDER}}', '[性别]')
        # 删除PROTAGONIST2的整个设定块
        template_content = remove_protagonist2_from_template(template_content)
    
    # 可以根据novel_genre和writing_element进行额外的动态替换
    # 例如：不同类型的小说可以有不同的字数要求等
    print(f"生成设定 - 小说类型: {novel_genre}, 写作元素: {writing_element}, 主角信息: {protagonists}")
    
    # 构建系统提示词
    
    # 添加写作元素信息处理
    writing_element_info = ""
    if writing_element:
        element_data = get_writing_element_info(writing_element)
        if element_data:
            writing_element_info = f"""

**写作元素指导（不要输出此部分内容）：**
- 写作元素：{element_data['name']}
- 所属分类：{element_data['category']}
- 详细描述：{element_data['description']}

请在生成小说设定时严格遵循上述写作元素的风格和特点，确保故事情节、人物设定、冲突设计等方面都体现出该写作元素的核心特色，但不要将写作元素模块内容包含在最终输出中。"""
    
    humanization_instruction = ""
    if humanization_content:
        humanization_instruction = f"""

**人类化创作指导（不要输出此部分内容）：**
{humanization_content}

请在生成小说设定时遵循上述人类化要求，但不要将人类化模块内容包含在最终输出中。"""


    # 构建角色信息重要提醒
    protagonist_reminder = ""
    if protagonists:
        protagonist_names = []
        for i, p in enumerate(protagonists, 1):
            protagonist_names.append(f"主角{i}：{p['name']}（{p['gender']}）")
        protagonist_reminder = f"""
**重要：角色设定优先级说明**
本次创作的角色设定如下，请严格使用这些角色信息，不要被后续灵感内容中的任何角色名称影响：
{chr(10).join(protagonist_names)}

在生成的小说设定中，必须使用上述指定的角色姓名，不得使用其他任何角色姓名。"""

    system_prompt = f"""你是专业的小说设定生成专家。请根据用户的需求，严格按照以下模板生成完整的小说设定。{protagonist_reminder}{writing_element_info}{humanization_instruction}

模板结构：
{template_content}
"""
    
    # 对系统提示词进行模板替换
    system_prompt = template_manager.replace_template(system_prompt)
    
    # 调试：验证模板替换结果  
    print(f"\n=== 调试：验证模板替换结果 ===")
    if "12章" in system_prompt and "1200-1500字" in system_prompt:
        print("✅ 模板变量替换成功！")
    else:
        print("❌ 模板变量替换失败，检查配置...")
        if "{{" in system_prompt:
            print(f"❌ 仍有未替换的双大括号变量")
        if "{" in system_prompt and "}" in system_prompt:
            print(f"❌ 检测到可能的未替换变量")
    print("=" * 40)

    # 保存提示词到文件（静默保存，不影响控制台输出）
    save_prompts_quiet(system_prompt, user_prompt, "story_setting")
    
    # 根据API类型选择合适的max_tokens
    max_tokens = get_max_tokens()
    
    # 根据API类型调用不同的接口
    api_type = CONFIG.get('api_type', 'ark')
    
    if api_type == 'ark':
        # ARK API 调用
        thinking_config = {"type": "disabled"} if CONFIG['generation'].get('thinking_disabled', True) else {}
        
        resp = client.chat.completions.create(
            model=CONFIG['api']['model'],
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            thinking=thinking_config,
            temperature=CONFIG['generation']['temperature'],
            max_tokens=max_tokens,
            top_p=CONFIG['generation']['top_p'],
            stream=True,
            stream_options=ChatCompletionStreamOptionsParam(
                {"include_usage":True, "chunk_include_usage":True},
                total=True
            ),
        )
    elif api_type == 'openai':
        # OpenAI API 调用
        extra_body = {}
        if CONFIG['generation'].get('thinking_disabled', True):
            extra_body["chat_template_kwargs"] = {"enable_thinking": False}
            
        resp = client.chat.completions.create(
            model=CONFIG['api']['model'],
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            temperature=CONFIG['generation']['temperature'],
            max_tokens=max_tokens,
            top_p=CONFIG['generation']['top_p'],
            stream=True,
            stream_options={"include_usage": True},
            extra_body=extra_body,
        )
    else:
        raise ValueError(f"不支持的API类型: {api_type}")
    
    return resp

def generate_and_save(user_prompt, output_filename=None, protagonists=None):
    """生成并保存小说设定"""
    if output_filename is None:
        # 使用config.yaml中的story_source作为默认输出文件
        output_filename = CONFIG['files'].get('story_source', CONFIG['files']['output_file'])
    
    print("正在生成小说设定...")
    
    resp = generate_story_setting(user_prompt, protagonists=protagonists)
    
    story_setting = ""
    usage_info = None
    
    try:
        for chunk in resp:
            # 处理内容块
            if chunk.choices:
                content = chunk.choices[0].delta.content
                if content:
                    print(content, end="", flush=True)
                    story_setting += content
            
            # 捕获使用信息
            if hasattr(chunk, 'usage') and chunk.usage:
                usage_info = chunk.usage
    
    except Exception as e:
        print(f"\n生成错误: {e}")
        return
    
    # 保存到文件
    with open(output_filename, 'w', encoding='utf-8') as f:
        f.write(story_setting)
    
    print(f"\n\n=== 生成完成 ===")
    print(f"小说设定已保存到: {output_filename}")
    print(f"总字符数: {len(story_setting)}")
    
    if usage_info:
        print(f"\n=== Token 使用统计 ===")
        print(f"输入 tokens: {usage_info.prompt_tokens}")
        print(f"输出 tokens: {usage_info.completion_tokens}")
        print(f"总计 tokens: {usage_info.total_tokens}")

if __name__ == "__main__":
    print("=== 小说设定生成器 ===")
    
    # 获取用户输入
    user_prompt = input("请输入您的小说创意描述: ")
    if not user_prompt.strip():
        print("错误: 请输入有效的创意描述")
        exit(1)
    
    # 可选：设置主角信息
    print("\n--- 可选：主角信息设置 ---")
    protagonist1_name = input("主角姓名 (回车跳过): ").strip()
    protagonist1_gender = input("主角性别 (男/女/其他，回车跳过): ").strip()
    
    protagonists = None
    if protagonist1_name:
        protagonists = [{"name": protagonist1_name, "gender": protagonist1_gender or "男"}]
        
        # 询问是否添加第二个主角
        add_second = input("是否添加第二个主角? (y/n): ").strip().lower()
        if add_second == 'y':
            protagonist2_name = input("第二个主角姓名: ").strip()
            protagonist2_gender = input("第二个主角性别 (男/女/其他): ").strip()
            if protagonist2_name:
                protagonists.append({"name": protagonist2_name, "gender": protagonist2_gender or "女"})
    
    # 生成并保存
    generate_and_save(user_prompt, protagonists=protagonists)