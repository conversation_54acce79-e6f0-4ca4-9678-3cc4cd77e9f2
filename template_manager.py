import re
import yaml
from typing import Dict, Any

class TemplateManager:
    """模板变量管理器，支持{{xxx}}格式的变量替换"""
    
    def __init__(self, config_path="config.yaml"):
        self.config_path = config_path
        self.variables = {}
        self.load_variables()
    
    def load_variables(self):
        """从配置文件加载模板变量"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 加载模板变量
            if 'template_variables' in config:
                self.variables.update(config['template_variables'])
            
            # 从其他配置节自动生成变量
            self._auto_generate_variables(config)
            
        except Exception as e:
            print(f"警告: 加载模板变量失败 ({e})，使用默认变量")
            self._load_default_variables()
    
    def _auto_generate_variables(self, config: Dict[str, Any]):
        """从配置文件自动生成变量"""
        # 从续写控制生成变量
        if 'continue' in config:
            cont = config['continue']
            # 不要覆盖template_variables中已定义的变量，只在缺失时提供默认值
            if 'TOTAL_MIN_CHARS' not in self.variables:
                self.variables['TOTAL_MIN_CHARS'] = str(cont.get('min_chars', 48000))
            if 'MAX_CONTINUES' not in self.variables:
                self.variables['MAX_CONTINUES'] = str(cont.get('max_continues', 5))
        
        # 从生成参数生成变量
        if 'generation' in config:
            gen = config['generation']
            self.variables.update({
                'TEMPERATURE': str(gen.get('temperature', 0.7)),
                'MAX_TOKENS': str(gen.get('max_tokens', 12288)),
            })
        
        # 从状态标识符生成变量
        if 'status' in config:
            status = config['status']
            self.variables.update({
                'COMPLETE_MARKER': status.get('complete_marker', '[STORY_COMPLETE]'),
            })
    
    def _load_default_variables(self):
        """加载默认变量"""
        self.variables = {
            'MIN_CHARS_PER_CHAPTER': '800',
            'MAX_CHARS_PER_CHAPTER': '1000',
            'TOTAL_CHAPTERS': '12',
            'TOTAL_MIN_CHARS': '9600',
            'MAX_TOTAL_CHARS': '12000',
            'CHAPTER_RANGE': '800-1000',
            'WORD_REQUIREMENT': '每章必须达到800-1000中文字，绝不能少于800字',
            'CHAPTER_START_CHARS': '200',
            'CHAPTER_MIDDLE_CHARS_MIN': '400',
            'CHAPTER_MIDDLE_CHARS_MAX': '600',
            'CHAPTER_END_CHARS': '200',
            'PROLOGUE_RANGE': '600-800',
            'PROLOGUE_START': '150',
            'PROLOGUE_MIDDLE_MIN': '300',
            'PROLOGUE_MIDDLE_MAX': '500',
            'PROLOGUE_END': '150',
            'COMPLETE_MARKER': '[STORY_COMPLETE]',
        }
    
    def add_variable(self, key: str, value: str):
        """添加或更新变量"""
        self.variables[key] = value
    
    def get_variable(self, key: str, default: str = ""):
        """获取变量值"""
        return self.variables.get(key, default)
    
    def replace_template(self, text: str) -> str:
        """替换文本中的{{xxx}}模板变量"""
        def replace_match(match):
            var_name = match.group(1).strip()
            return self.variables.get(var_name, match.group(0))  # 找不到变量时保持原样
        
        # 匹配{{xxx}}格式的变量
        pattern = r'\{\{\s*([^}]+)\s*\}\}'
        return re.sub(pattern, replace_match, text)
    
    def process_file(self, file_path: str) -> str:
        """处理文件中的模板变量并返回替换后的内容"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            return self.replace_template(content)
        except FileNotFoundError:
            print(f"警告: 文件 {file_path} 未找到")
            return ""
        except Exception as e:
            print(f"警告: 处理文件 {file_path} 时出错: {e}")
            return ""
    
    def save_processed_file(self, input_file: str, output_file: str):
        """处理模板文件并保存到输出文件"""
        processed_content = self.process_file(input_file)
        if processed_content:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(processed_content)
            print(f"已处理模板文件: {input_file} -> {output_file}")
    
    def list_variables(self):
        """列出所有可用变量"""
        print("可用的模板变量：")
        for key, value in sorted(self.variables.items()):
            print(f"  {{{{{key}}}}} : {value}")
    
    def validate_template(self, text: str) -> list:
        """验证模板中是否有未定义的变量"""
        pattern = r'\{\{\s*([^}]+)\s*\}\}'
        matches = re.findall(pattern, text)
        undefined_vars = []
        
        for var_name in matches:
            var_name = var_name.strip()
            if var_name not in self.variables:
                undefined_vars.append(var_name)
        
        return undefined_vars

# 全局模板管理器实例
template_manager = TemplateManager()

def replace_template_variables(text: str) -> str:
    """便捷函数：替换文本中的模板变量"""
    return template_manager.replace_template(text)

if __name__ == "__main__":
    # 测试功能
    tm = TemplateManager()
    tm.list_variables()
    
    # 测试模板替换
    test_text = "每章需要{{MIN_CHARS_PER_CHAPTER}}-{{MAX_CHARS_PER_CHAPTER}}字，共{{TOTAL_CHAPTERS}}章"
    print(f"\n测试替换：")
    print(f"原文: {test_text}")
    print(f"替换后: {tm.replace_template(test_text)}")