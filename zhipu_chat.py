from openai import OpenAI
import os
import time
import json
from datetime import datetime
import requests

# ================================
# 配置区域 - 在这里修改所有设置
# ================================

# API 提供商配置
API_PROVIDERS = {
    "bigmodel": {
        "api_key": "xxx",
        "base_url": "https://open.bigmodel.cn/api/paas/v4",
        "timeout": 60,
        "models": {
            "glm-4.5": {"max_tokens": 96000, "supports_thinking": True}
        }
    },
    "alibaba": {
        "api_key": "xxxx",
        "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",
        "timeout": 60,
        "models": {
            "glm-4.5": {"max_tokens": 16384, "supports_thinking": False}
        }
    },
    "ucloud": {
        "api_key": "4LMhKOIkYP7vdvLZ114fCfD5-9F65-4091-a9d2-048870A5",
        "base_url": "https://api.modelverse.cn/v1",
        "timeout": 60,
        "models": {
            "glm-4.5": {"max_tokens": 96000, "supports_thinking": False}
        },
        "model_mapping": {
            "glm-4.5": "zai-org/glm-4.5"
        }
    }
}

# 当前选择的API提供商
CURRENT_PROVIDER = "ucloud"  # 可选: "bigmodel" 或 "alibaba"

# 模型配置
MODEL_CONFIG = {
    "model": "glm-4.5",  # 统一使用 glm-4.5-air 模型
    "temperature": 0.6,      # 创造性控制 (0.0-1.0)
    "max_tokens": 16384,     # 最大输出token数 (会根据模型和提供商自动调整)
    "top_p": 0.95,          # 采样参数 (0.0-1.0)
    "stream": True          # 是否启用流式输出
}

# Thinking模式配置
THINKING_CONFIG = {
    "thinking_disabled": True,   # 是否禁用thinking模式
    "show_thinking": False,      # 是否显示thinking过程
    "save_thinking": True        # 是否保存thinking内容到文件
}

# 文件配置
FILE_CONFIG = {
    "system_prompt_file": "generated_prompt.md",  # 系统提示文件路径（markdown格式）
    "user_prompt_file": "user_prompt.md",          # 用户提示文件路径（markdown格式）
    "output_story_file": "generated_story.txt",    # 生成故事保存文件
    "thinking_output_file": "thinking_content.txt"  # thinking内容保存文件
}

# 并发配置
CONCURRENCY_CONFIG = {
    "enabled": True,         # 是否启用并发（默认启用）
    "max_concurrent": 10,    # 最大并发数（默认50个）
    "concurrency_type": "threads",  # 并发类型: "threads" 或 "processes"
    "output_dir": "concurrent_results",  # 并发结果存储目录
    "auto_cleanup": True,    # 是否自动删除上次运行的结果
    "aggregate_realtime": False,  # 是否写入聚合的实时文件
    "aggregate_filename": "realtime_output.txt"  # 聚合实时文件名
}

# ================================
# 以下代码无需修改
# ================================

def get_current_provider_config():
    """获取当前选择的API提供商配置"""
    if CURRENT_PROVIDER not in API_PROVIDERS:
        raise ValueError(f"未知的API提供商: {CURRENT_PROVIDER}")
    return API_PROVIDERS[CURRENT_PROVIDER]

def get_model_info(model_name):
    """获取指定模型的信息"""
    provider_config = get_current_provider_config()
    if model_name not in provider_config["models"]:
        print(f"警告: 模型 {model_name} 不在 {CURRENT_PROVIDER} 的支持列表中")
        return {"max_tokens": MODEL_CONFIG["max_tokens"], "supports_thinking": False}
    return provider_config["models"][model_name]

def get_actual_model_name(model_name):
    """获取实际API调用时使用的模型名称（考虑模型映射）"""
    provider_config = get_current_provider_config()
    model_mapping = provider_config.get("model_mapping", {})
    return model_mapping.get(model_name, model_name)

def create_concurrent_requests(request_params, num_requests=1):
    """创建并发请求参数列表"""
    requests = []
    for i in range(num_requests):
        # 为每个请求添加唯一标识
        request_copy = request_params.copy()
        request_copy["extra_body"] = request_copy.get("extra_body", {}).copy()
        request_copy["extra_body"]["request_id"] = f"req_{i+1}"
        requests.append(request_copy)
    return requests

def setup_concurrent_output_dir():
    """设置并发输出目录"""
    import os
    import shutil
    from datetime import datetime
    
    # 创建基础目录
    base_dir = CONCURRENCY_CONFIG["output_dir"]
    
    # 根据配置决定是否删除上次运行的结果
    if CONCURRENCY_CONFIG["auto_cleanup"] and os.path.exists(base_dir):
        try:
            shutil.rmtree(base_dir)
            print(f"🗑️ 已删除上次运行的结果目录: {base_dir}")
        except Exception as e:
            print(f"⚠️ 删除上次结果时出错: {e}")
    
    # 创建目录（如果不存在或已被删除）
    if not os.path.exists(base_dir):
        os.makedirs(base_dir, exist_ok=True)
    
    # 创建时间戳子目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    session_dir = os.path.join(base_dir, f"session_{timestamp}")
    os.makedirs(session_dir, exist_ok=True)
    
    print(f"📁 并发结果将保存到: {session_dir}")
    return session_dir

def make_http_chat_request(request_params):
    """使用HTTP请求方式发送聊天请求，能够获取完整的响应头信息"""
    provider_config = get_current_provider_config()
    
    # 构建请求URL
    url = f"{provider_config['base_url']}/chat/completions"
    
    # 构建请求头
    headers = {
        "Authorization": f"Bearer {provider_config['api_key']}",
        "Content-Type": "application/json",
        "Accept": "text/event-stream" if request_params.get("stream", False) else "application/json"
    }
    
    # 处理请求参数，确保thinking参数正确传递
    json_data = request_params.copy()
    
    # 如果存在extra_body，将其内容合并到主请求体中
    if "extra_body" in json_data:
        extra_body = json_data.pop("extra_body")
        # 合并所有thinking相关参数
        for key, value in extra_body.items():
            json_data[key] = value
    
    # 发送POST请求
    response = requests.post(
        url=url,
        headers=headers,
        json=json_data,
        stream=request_params.get("stream", False),
        timeout=provider_config.get("timeout", 60)
    )
    
    # 检查响应状态
    response.raise_for_status()
    
    return response

def process_http_stream_response(response, req_id="", print_lock=None, output_file=None, per_req_content_file=None, per_req_thinking_file=None):
    """处理HTTP流式响应 - 支持实时打印和写入
    output_file: （可选）聚合实时输出文件
    per_req_content_file: （可选）当前请求的content文件，实时追加
    per_req_thinking_file: （可选）当前请求的thinking文件，实时追加
    """
    content = ""
    thinking_content = ""
    
    # 调试：检查输出文件
    if output_file and print_lock:
        try:
            # 测试文件写入权限
            with open(output_file, "a", encoding="utf-8") as f:
                f.write(f"\n[{req_id}开始] 流式处理开始 - {datetime.now().strftime('%H:%M:%S')}\n")
                f.flush()
            with print_lock:
                print(f"🔧 [{req_id}] 输出文件检查通过: {output_file}")
        except Exception as e:
            with print_lock:
                print(f"❌ [{req_id}] 输出文件检查失败: {output_file}, 错误: {e}")
    # 调试：检查按请求文件
    if per_req_content_file and print_lock:
        try:
            with open(per_req_content_file, "a", encoding="utf-8") as f:
                f.write("")
            with print_lock:
                print(f"🔧 [{req_id}] 内容文件准备就绪: {per_req_content_file}")
        except Exception as e:
            with print_lock:
                print(f"❌ [{req_id}] 内容文件不可写: {e}")
    if per_req_thinking_file and print_lock:
        try:
            with open(per_req_thinking_file, "a", encoding="utf-8") as f:
                f.write("")
            with print_lock:
                print(f"🔧 [{req_id}] 思考文件准备就绪: {per_req_thinking_file}")
        except Exception as e:
            with print_lock:
                print(f"❌ [{req_id}] 思考文件不可写: {e}")
    
    try:
        # 手动解析Server-Sent Events流
        buffer = ""
        
        for chunk in response.iter_content(chunk_size=1024, decode_unicode=True):
            if chunk:
                buffer += chunk
                
                # 按行分割
                while '\n' in buffer:
                    line, buffer = buffer.split('\n', 1)
                    line = line.strip()
                    
                    # 处理SSE格式的数据
                    if line.startswith('data: '):
                        data = line[6:]  # 去掉 'data: ' 前缀
                        
                        if data == '[DONE]' or data == '[done]':
                            return content, thinking_content
                            
                        try:
                            chunk_data = json.loads(data)
                            
                            if "choices" in chunk_data and len(chunk_data["choices"]) > 0:
                                choice = chunk_data["choices"][0]
                                delta = choice.get("delta", {})
                                
                                # 处理thinking内容
                                thinking_text = delta.get("thinking") or delta.get("reasoning_content") or delta.get("reasoning")
                                if thinking_text:
                                    thinking_content += thinking_text
                                    # 实时打印thinking内容
                                    if print_lock:
                                        with print_lock:
                                            print(f"[{req_id}思考] {thinking_text}", end="", flush=True)
                                    # 实时写入文件
                                    if output_file:
                                        try:
                                            with open(output_file, "a", encoding="utf-8") as f:
                                                f.write(f"[{req_id}思考] {thinking_text}")
                                                f.flush()
                                        except Exception as e:
                                            if print_lock:
                                                with print_lock:
                                                    print(f"\n[{req_id}警告] 写入thinking内容失败: {e}")
                                    if per_req_thinking_file:
                                        try:
                                            with open(per_req_thinking_file, "a", encoding="utf-8") as f:
                                                f.write(thinking_text)
                                                f.flush()
                                        except Exception as e:
                                            if print_lock:
                                                with print_lock:
                                                    print(f"\n[{req_id}警告] 写入thinking文件失败: {e}")
                                
                                # 处理常规内容
                                if delta.get("content"):
                                    content += delta["content"]
                                    # 实时打印内容
                                    if print_lock:
                                        with print_lock:
                                            print(f"[{req_id}] {delta['content']}", end="", flush=True)
                                    # 实时写入文件
                                    if output_file:
                                        try:
                                            with open(output_file, "a", encoding="utf-8") as f:
                                                f.write(f"[{req_id}] {delta['content']}")
                                                f.flush()
                                        except Exception as e:
                                            if print_lock:
                                                with print_lock:
                                                    print(f"\n[{req_id}警告] 写入内容失败: {e}")
                                    if per_req_content_file:
                                        try:
                                            with open(per_req_content_file, "a", encoding="utf-8") as f:
                                                f.write(delta['content'])
                                                f.flush()
                                        except Exception as e:
                                            if print_lock:
                                                with print_lock:
                                                    print(f"\n[{req_id}警告] 写入content文件失败: {e}")
                                    
                        except json.JSONDecodeError:
                            continue
                
    except Exception as e:
        if print_lock:
            with print_lock:
                print(f"\n[{req_id}错误] 流式处理错误: {e}")
    
    return content, thinking_content

def save_concurrent_result(session_dir, request_id, content, thinking_content, headers=None, error=None):
    """保存单个并发请求的结果"""
    import os
    import json
    
    # 创建请求专用目录
    req_dir = os.path.join(session_dir, request_id)
    os.makedirs(req_dir, exist_ok=True)
    
    # 保存内容（若已实时写入，追加为空或保持不变；此处确保文件存在）
    content_path = os.path.join(req_dir, "content.txt")
    if content:
        with open(content_path, "a", encoding="utf-8") as f:
            f.write(content)
    else:
        # 确保文件存在
        if not os.path.exists(content_path):
            open(content_path, "w", encoding="utf-8").close()
    
    # 保存thinking内容（同上）
    thinking_path = os.path.join(req_dir, "thinking.txt")
    if thinking_content:
        with open(thinking_path, "a", encoding="utf-8") as f:
            f.write(thinking_content)
    else:
        if not os.path.exists(thinking_path):
            open(thinking_path, "w", encoding="utf-8").close()
    
    # 保存所有响应头信息
    if headers:
        with open(os.path.join(req_dir, "headers.json"), "w", encoding="utf-8") as f:
            json.dump(dict(headers), f, indent=2, ensure_ascii=False)
    
    # 保存错误信息
    if error:
        with open(os.path.join(req_dir, "error.txt"), "w", encoding="utf-8") as f:
            f.write(str(error))
    
    # 计算最终写入长度（从文件读）
    try:
        with open(content_path, "r", encoding="utf-8") as f:
            final_content = f.read()
    except Exception:
        final_content = content or ""
    try:
        with open(thinking_path, "r", encoding="utf-8") as f:
            final_thinking = f.read()
    except Exception:
        final_thinking = thinking_content or ""

    # 创建元数据文件（包含所有响应头信息）
    metadata = {
        "request_id": request_id,
        "timestamp": datetime.now().isoformat(),
        "success": error is None,
        "content_length": len(final_content),
        "thinking_length": len(final_thinking),
        "headers": dict(headers) if headers else {},  # 直接保存所有响应头
        "error": str(error) if error else None
    }
    
    with open(os.path.join(req_dir, "metadata.json"), "w", encoding="utf-8") as f:
        json.dump(metadata, f, indent=2, ensure_ascii=False)

# 初始化 OpenAI 客户端
provider_config = get_current_provider_config()
client = OpenAI(
    api_key=provider_config["api_key"],
    base_url=provider_config["base_url"],
    timeout=provider_config["timeout"]
)

# 从文件读取提示内容
def load_system_prompt(file_path=None):
    """从markdown文件中读取系统提示"""
    if file_path is None:
        file_path = FILE_CONFIG["system_prompt_file"]
    
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            return file.read().strip()
    except FileNotFoundError:
        print(f"警告：系统提示文件 '{file_path}' 未找到，使用默认提示。")
        return "你是一个有用的AI助手。"
    except Exception as e:
        print(f"读取系统提示文件时出错：{e}")
        return "你是一个有用的AI助手。"

def load_user_prompt(file_path=None):
    """从markdown文件中读取用户提示"""
    if file_path is None:
        file_path = FILE_CONFIG["user_prompt_file"]
    
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            return file.read().strip()
    except FileNotFoundError:
        print(f"警告：用户提示文件 '{file_path}' 未找到，使用默认提示。")
        return "请帮助我完成任务。"
    except Exception as e:
        print(f"读取用户提示文件时出错：{e}")
        return "请帮助我完成任务。"

def process_stream(response, show_progress=True, show_thinking=False):
    """处理流式响应，分离内容和思考过程 - 基于 improved_reasoning.py 的逻辑"""
    content = ""
    thinking_content = ""
    chunk_count = 0
    first_chunk_received = False
    thinking_started = False
    stream_finished = False
    
    try:
        for chunk in response:
            chunk_count += 1
            
            # 检查流是否结束
            if hasattr(chunk, 'choices') and chunk.choices:
                choice = chunk.choices[0]
                
                # 检查finish_reason
                if hasattr(choice, 'finish_reason') and choice.finish_reason:
                    if show_progress:
                        print(f"\n[流结束原因: {choice.finish_reason}]", flush=True)
                    stream_finished = True
                    break
                
                # 检查是否有错误
                if hasattr(choice, 'delta') and hasattr(choice.delta, 'content') and choice.delta.content is None:
                    # 空内容块，可能是结束信号
                    continue
                
                # 检查delta内容
                delta = choice.delta
                
                # 检查是否有done状态（某些API可能返回[done]作为结束标记）
                if hasattr(delta, 'content') and delta.content == "[done]":
                    if show_progress:
                        print(f"\n[检测到结束标记: [done]]", flush=True)
                    stream_finished = True
                    break
                
                # 检查其他可能的结束标记
                if hasattr(delta, 'content') and delta.content in ["[done]", "[DONE]", "[end]", "[END]", "done", "end"]:
                    if show_progress:
                        print(f"\n[检测到结束标记: {delta.content}]", flush=True)
                    stream_finished = True
                    break
                
                # Handle thinking content - 支持多种字段名
                thinking_text = None
                
                # OpenAI compatible API thinking fields
                if hasattr(delta, 'thinking') and delta.thinking:
                    thinking_text = delta.thinking
                # GLM model uses reasoning_content for thinking
                elif hasattr(delta, 'reasoning_content') and delta.reasoning_content:
                    thinking_text = delta.reasoning_content
                # Some OpenAI providers might use different field names
                elif hasattr(delta, 'reasoning') and delta.reasoning:
                    thinking_text = delta.reasoning
                
                # Process thinking content if found
                if thinking_text:
                    thinking_content += thinking_text
                    if show_thinking and show_progress:
                        # 首次开始thinking时显示标题
                        if not thinking_started:
                            print("\n\n=== 模型思考过程 ===", flush=True)
                            thinking_started = True
                        # 直接显示thinking内容，不加前缀
                        print(thinking_text, end="", flush=True)
                
                # Handle regular content
                if hasattr(delta, 'content') and delta.content:
                    # 如果刚从thinking切换到内容，先换行分隔
                    if thinking_started and show_thinking and show_progress:
                        print("\n\n=== 生成内容 ===", flush=True)
                        thinking_started = False
                    
                    if show_progress:
                        print(delta.content, end="", flush=True)
                    content += delta.content
                    
    except Exception as e:
        print(f"\n[流式处理错误: {e}]")
        if not content:
            content = f"\n[第{chunk_count}次遇到错误，尝试继续...]\n"
    
    # 如果流正常结束，显示完成信息
    if stream_finished and show_progress:
        print(f"\n[流式输出完成，共处理 {chunk_count} 个数据块]")
    
    return content, thinking_content

# 加载提示内容
system_prompt = load_system_prompt()
user_prompt = load_user_prompt()
print(f"📄 已加载系统提示 (generated_prompt.md)，长度：{len(system_prompt)} 字符")
print(f"📄 已加载用户提示 (user_prompt.md)，长度：{len(user_prompt)} 字符")

# 获取模型信息并验证配置
model_info = get_model_info(MODEL_CONFIG["model"])
current_max_tokens = min(MODEL_CONFIG["max_tokens"], model_info["max_tokens"])

print(f"\n=== API配置信息 ===")
print(f"当前提供商: {CURRENT_PROVIDER}")
print(f"配置模型: {MODEL_CONFIG['model']}")
actual_model_name = get_actual_model_name(MODEL_CONFIG["model"])
if actual_model_name != MODEL_CONFIG["model"]:
    print(f"实际模型: {actual_model_name}")
print(f"模型最大tokens: {model_info['max_tokens']}")
print(f"实际使用tokens: {current_max_tokens}")
print(f"支持thinking: {model_info['supports_thinking']}")

# 创建聊天请求
print("\n开始生成内容...")
start_time = time.time()

# 构建请求参数 - 使用配置中的参数
actual_model_name = get_actual_model_name(MODEL_CONFIG["model"])
request_params = {
    "model": actual_model_name,
    "messages": [
        {
            "role": "system",
            "content": system_prompt
        },
        {
            "role": "user",
            "content": user_prompt
        }
    ],
    "temperature": MODEL_CONFIG["temperature"],
    "max_tokens": current_max_tokens,
    "top_p": MODEL_CONFIG["top_p"],
    "stream": MODEL_CONFIG["stream"],
    "stream_options": {"include_usage": True}
}

# 根据配置添加thinking相关参数 - 兼容多种平台
if THINKING_CONFIG["thinking_disabled"]:
    request_params["extra_body"] = {
        # 智谱AI兼容参数
        "thinking": {"type": "disabled"},
        "chat_template_kwargs": {"enable_thinking": False},
        "enable_thinking": False,
        
        # 其他平台兼容参数
        "reasoning": False,
        "reasoning_enabled": False,
        "use_reasoning": False,
        "thinking_enabled": False,
        "use_thinking": False,
        
        # 阿里云兼容参数
        "enable_reasoning": False,
        "reasoning_type": "disabled",
        
        # 通用参数
        "disable_thinking": True,
        "disable_reasoning": True,
        "no_thinking": True,
        "no_reasoning": True
    }
    print("🚫 已禁用thinking模式（兼容多平台）")
else:
    request_params["extra_body"] = {
        # 智谱AI兼容参数
        "thinking": {"type": "enabled"},
        "chat_template_kwargs": {"enable_thinking": True},
        "enable_thinking": True,
        
        # 其他平台兼容参数
        "reasoning": True,
        "reasoning_enabled": True,
        "use_reasoning": True,
        "thinking_enabled": True,
        "use_thinking": True,
        
        # 阿里云兼容参数
        "enable_reasoning": True,
        "reasoning_type": "enabled",
        
        # 通用参数
        "disable_thinking": False,
        "disable_reasoning": False,
        "no_thinking": False,
        "no_reasoning": False
    }
    print("✅ thinking模式已启用（兼容多平台）")

# 并发处理逻辑
if CONCURRENCY_CONFIG["enabled"]:
    print(f"🔄 启用并发模式: {CONCURRENCY_CONFIG['max_concurrent']} 个并发请求")
    
    # 设置输出目录
    session_dir = setup_concurrent_output_dir()
    
    # （可选）创建聚合实时输出文件
    realtime_output_file = None
    if CONCURRENCY_CONFIG.get("aggregate_realtime", False):
        realtime_output_file = os.path.join(session_dir, CONCURRENCY_CONFIG.get("aggregate_filename", "realtime_output.txt"))
        with open(realtime_output_file, "w", encoding="utf-8") as f:
            f.write(f"=== 实时推理结果 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} ===\n\n")
        print(f"📝 实时聚合结果将写入到: {realtime_output_file}")
    
    # 创建并发请求列表
    concurrent_requests = create_concurrent_requests(
        request_params, 
        CONCURRENCY_CONFIG["max_concurrent"]
    )
    
    # 使用线程池处理并发请求
    import concurrent.futures
    from threading import Lock
    
    results = []
    print_lock = Lock()
    
    def process_single_request(req_params, req_id):
        """处理单个并发请求 - 使用HTTP请求方式"""
        try:
            # 使用HTTP请求方式获取响应
            response = make_http_chat_request(req_params)
            
            # 获取所有响应头信息
            headers = dict(response.headers)
            
            # 打印X-Modelverse-Request-Id头（如果存在）
            request_id_header = headers.get('X-Modelverse-Request-Id') or headers.get('x-modelverse-request-id')
            if request_id_header:
                with print_lock:
                    print(f"🆔 请求 {req_id} 的 X-Modelverse-Request-Id: {request_id_header}")
            
            with print_lock:
                print(f"📋 请求 {req_id} 获取到 {len(headers)} 个响应头")
                print(f"🚀 请求 {req_id} 开始流式输出...")
            
            # 为本请求准备独立的实时文件路径（在其req目录内）
            req_dir = os.path.join(session_dir, req_id)
            os.makedirs(req_dir, exist_ok=True)
            per_req_content_file = os.path.join(req_dir, "content.txt")
            per_req_thinking_file = os.path.join(req_dir, "thinking.txt")

            # 处理流式响应 - 传递实时打印，并实时写入到各自文件
            content, thinking_content = process_http_stream_response(
                response,
                req_id=req_id,
                print_lock=print_lock,
                output_file=realtime_output_file,  # 若启用聚合，也写入聚合文件
                per_req_content_file=per_req_content_file,
                per_req_thinking_file=per_req_thinking_file
            )

            # 将已累计的内容写入独立文件（若已存在则追加，确保最终一致）
            if content:
                with open(per_req_content_file, "a", encoding="utf-8") as f:
                    f.write(content)
                    f.flush()
            if thinking_content:
                with open(per_req_thinking_file, "a", encoding="utf-8") as f:
                    f.write(thinking_content)
                    f.flush()
            
            # 保存结果到文件
            save_concurrent_result(session_dir, req_id, content, thinking_content, headers)
            
            # （可选）写入完成标记到聚合实时输出文件
            if realtime_output_file:
                try:
                    with open(realtime_output_file, "a", encoding="utf-8") as f:
                        f.write(f"\n\n[{req_id}完成] 生成长度: {len(content)} 字符\n")
                        f.write("=" * 50 + "\n\n")
                        f.flush()
                except Exception:
                    pass
            
            with print_lock:
                print(f"\n✅ 请求 {req_id} 完成，生成长度: {len(content)} 字符")
            
            return {
                "request_id": req_id,
                "content": content,
                "thinking_content": thinking_content,
                "headers": headers,
                "success": True
            }
        except Exception as e:
            # 保存错误信息
            save_concurrent_result(session_dir, req_id, "", "", None, e)
            
            with print_lock:
                print(f"❌ 请求 {req_id} 失败: {e}")
            return {
                "request_id": req_id,
                "content": "",
                "thinking_content": "",
                "headers": {},
                "success": False,
                "error": str(e)
            }
    
    # 执行并发请求
    with concurrent.futures.ThreadPoolExecutor(max_workers=CONCURRENCY_CONFIG["max_concurrent"]) as executor:
        futures = []
        for i, req_params in enumerate(concurrent_requests):
            future = executor.submit(process_single_request, req_params, f"req_{i+1:03d}")
            futures.append(future)
        
        # 等待所有请求完成
        for future in concurrent.futures.as_completed(futures):
            results.append(future.result())
    
    # 合并结果
    content = "\n\n".join([r["content"] for r in results if r["success"]])
    thinking_content = "\n\n".join([r["thinking_content"] for r in results if r["success"]])
    
    print(f"📊 并发请求完成: {len([r for r in results if r['success']])}/{len(results)} 成功")
    print(f"📁 所有结果已保存到: {session_dir}")
    
else:
    # 单请求处理
    print("🔄 使用单请求模式")
    response = client.chat.completions.create(**request_params)
    
    # 处理流式响应
    content, thinking_content = process_stream(
        response, 
        show_progress=True, 
        show_thinking=THINKING_CONFIG["show_thinking"]
    )

# 显示统计信息
end_time = time.time()
print(f"\n\n=== 生成完成统计 ===")
print(f"生成时间: {end_time - start_time:.2f}秒")
print(f"正文字数: {len(content)} 字符")
print(f"思考内容长度: {len(thinking_content)} 字符")

# 保存生成的内容到文件
with open("generated_story.txt", "w", encoding="utf-8") as f:
    f.write(content)
print(f"正文内容已保存到: generated_story.txt")

# 如果有思考内容，也保存到文件
if thinking_content:
    with open("thinking_content.txt", "w", encoding="utf-8") as f:
        f.write(thinking_content)
    print(f"思考过程已保存到: thinking_content.txt")