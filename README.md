# AI 小说生成器

面向中文网文的长篇小说创作流水线，支持双引擎（火山引擎 ARK 与 OpenAI 兼容生态）、两阶段创作（设定→正文）、断点续写与流式输出，可通过 CLI 或 Gradio 可视化界面一键运行。

## 功能亮点

- **双引擎可切换**：`novel_api_type` 与 `setting_api_type` 分别独立控制正文与设定所用 API（ARK 或 OpenAI 兼容）。
- **两阶段创作**：先生成详细设定与章节细纲，再按细纲生成完整正文，过程全程流式展示。
- **断点续写**：自动定位最后完整章节（基于章节标题与截断判断），无缝 Continue，支持完成标识符 `[STORY_COMPLETE]`。
- **模板变量系统**：`config.yaml` 提供模板变量，`template_manager` 统一替换到提示词与设定模板中。
- **人类化写作注入**：可选 `humanization_module.md` 自动注入，使文风更自然。
- **提示词留档**：所有系统/用户提示词自动保存到 `prompts/`，便于复盘与调试。
- **Gradio UI**：内置可视化应用，一键生成设定与正文、实时观测进度与统计。

## 目录结构

```
ai-story/
├── config.yaml                  # 主配置（双引擎、模板变量、文件映射等）
├── application.yaml             # Gradio 应用的类型/元素配置（可选）
├── improved_reasoning.py        # 正文生成（流式 + 断点续写）
├── generate_story_setting.py    # 设定与章节细纲生成（流式）
├── gradio_app.py                # 可视化应用入口（端口 7860）
├── prompt.md                    # 主提示词（正文首轮）
├── continue_prompt.md           # 续写提示词（Continue）
├── humanization_module.md       # 人类化写作模块（可选注入）
├── story_rules.md               # 12 章模板
├── story_rules_24.md            # 24 章模板
├── story_rules_36.md            # 36 章模板
├── template_manager.py          # 模板变量替换
├── prompt_saver.py              # 提示词留档到 prompts/
├── novel_ideas.py               # 随机创意库（Gradio 用）
├── story.md                     # 你的设定与细纲（运行后生成/自备）
└── generated_novel.txt          # 生成的小说正文（运行后生成）
```

## 安装

```bash
pip install -r requirements.txt
```

## 配置（config.yaml）

示例（请替换为自己的密钥/模型，勿提交真实密钥到版本库）：

```yaml
# 分别指定正文与设定所用 API 类型：ark | openai
novel_api_type: "openai"
setting_api_type: "ark"

# ARK（供设定生成或正文生成使用）
api:        # generate_story_setting.py 默认读取
  key: "YOUR_ARK_API_KEY"
  model: "YOUR_ARK_MODEL_ID"
  timeout: 2400

api-novel:  # improved_reasoning.py 在 novel_api_type=ark 时读取
  key: "YOUR_ARK_API_KEY"
  model: "YOUR_ARK_MODEL_ID"
  timeout: 2400

# OpenAI 兼容（如 ModelVerse 等）
openaiapi:
  key: "YOUR_OPENAI_COMPAT_KEY"
  base_url: "https://api.your-provider.com/v1"
  model: "your-org/your-model"
  timeout: 2400

generation:
  temperature: 1.0
  top_p: 0.7
  thinking_disabled: true

token_limits:
  ark: 32768
  openai: 98304

continue:
  max_continues: 8
  empty_continue_limit: 2
  max_incomplete_continues: 8
  min_chars: 14400   # 参考下限；当前正文逻辑不强制此阈值，仅用于显示/规划

files:
  story_source: "story.md"
  main_prompt: "prompt.md"
  continue_prompt: "continue_prompt.md"
  output_file: "generated_novel.txt"
  humanization_module: "humanization_module.md"
  story_template: "story_rules_36.md"

status:
  complete_marker: "[STORY_COMPLETE]"

statistics:
  show_detailed_continue_stats: 5
  show_char_stats: true
  show_token_stats: true

# 模板变量（会被注入到提示词/模板）
template_variables:
  MIN_CHARS_PER_CHAPTER: "1200"
  MAX_CHARS_PER_CHAPTER: "1500"
  TOTAL_CHAPTERS: "36"
  TOTAL_MIN_CHARS: "14400"
  MAX_TOTAL_CHARS: "18000"
  CHAPTER_RANGE: "1200-1500"
  WORD_REQUIREMENT: "每章必须达到1200-1500中文字，绝不能少于1200字"
  PROLOGUE_RANGE: "1000-1200"
  COMPLETE_MARKER: "[STORY_COMPLETE]"
```

附注：Gradio 应用会读取 `application.yaml` 的类型/元素配置，用于界面下拉选项与元素描述。

## 快速开始

1) 生成小说设定与细纲（CLI）

```bash
python generate_story_setting.py
```

或使用可视化界面：

```bash
python gradio_app.py
# 浏览器打开 http://localhost:7860
```

2) 生成小说正文（CLI）

```bash
python improved_reasoning.py
```

运行后产物：
- 设定/细纲：`story.md`
- 正文：`generated_novel.txt`
- 提示词记录：`prompts/novel_main_system.md` 等

## 工作原理（要点）

- 设定与细纲：从 `story_rules_*.md` 模板抽取“输出模板结构”，结合人类化模块与可选写作元素信息，生成结构化的设定文档。
- 正文生成：
  - 首轮使用 `prompt.md`，将 `{{STORY_SETTING}}` 与 `{{CHAPTER_OUTLINE}}` 动态替换后流式生成。
  - 续写使用 `continue_prompt.md`，自动对齐下一章节，保持风格一致。
  - 章节判定：正文使用 `## 第X章 章节标题`（或 `## 序章`）作为锚点；结合 token 截断判断，拼接时自动去除不完整章节再续写。
- 完成判定：当检测到 `status.complete_marker`（默认 `[STORY_COMPLETE]`）即视为“可能已完成”。
- 留档：系统/用户提示词静默保存到 `prompts/` 目录，便于回溯与复用。

提示：`story.md` 必须包含“设定正文 + 章节细纲”两部分，程序通过正则定位 `##/### 小说细纲（\d+章）` 来拆分设定与细纲。

## 提示词与模板

- `prompt.md`：正文首轮提示词（会注入设定/大纲与模板变量）。
- `continue_prompt.md`：续写提示词（会注入设定/大纲与下一章节号）。
- `story_rules*.md`：12/24/36 章版本模板（Gradio 可选），用于生成结构化设定与章节细纲。
- `humanization_module.md`：写作“人类化”指导，将被自动注入到系统提示词。

## 常见问题（FAQ）

- 只生成了部分章节？
  - 检查是否过早出现 `[STORY_COMPLETE]`；必要时调整提示词或提高 `max_incomplete_continues`。
- 看起来“卡住”？
  - 流式生成存在自然间歇，关注控制台/Gradio 实时输出；如连续空块达到阈值会自动终止。
- 字数不足？
  - 当前逻辑以“完成标识 + 续写循环”判定为主；`min_chars` 仅作参考显示，不强制截断。
- 密钥安全？
  - 请使用环境注入/本地未入库文件管理密钥，不要将真实密钥提交到仓库。

## 许可证

本项目仅供学习与研究使用，请遵守相应 API/服务条款。

—— 开始你的 AI 小说创作之旅吧！