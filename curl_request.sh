#!/bin/bash

# Read file contents and escape for JSON
SYSTEM_CONTENT=$(cat prompts/novel_main_system.md | jq -R -s '.')
USER_CONTENT=$(cat prompts/novel_main_user.md | jq -R -s '.')

# API_KEY="7820fec1142362d562face7599b09e79.YCMlNnnxC1mEwjgy"
# API_URL="https://open.bigmodel.cn/api/paas/v4/chat/completions"
# MODEL="glm-4.5"

API_KEY="4LMhKOIkYP7vdvLZ114fCfD5-9F65-4091-a9d2-048870A5"
API_URL="https://api.modelverse.cn/v1/chat/completions"
MODEL="zai-org/glm-4.5"

# OpenAI API curl request based on config.yaml
curl -X POST "$API_URL" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $API_KEY" \
  -d '{
    "model": "'"$MODEL"'",
    "messages": [
      {
        "role": "system", 
        "content": '"$SYSTEM_CONTENT"'
      },
      {
        "role": "user", 
        "content": '"$USER_CONTENT"'
      }
    ],
    "temperature": 1.0,
    "top_p": 0.7,
    "max_tokens": 98304,
    "stream": true,
    "stream_options": {
      "include_usage": true
    },
    "thinking": {
      "type": "disabled"
    },
    "chat_template_kwargs": {
      "enable_thinking": false
    },
    "extra_body": {
      "enable_thinking": false
    }
  }'