# AI小说生成器配置文件

# API类型独立选择 - 可以为不同功能选择不同的API
# 可选值: 
#   "ark"    - 使用火山引擎ARK API (推荐，速度快，成本低)
#   "openai" - 使用OpenAI兼容API (兼容性好，支持更多提供商)

# 🎯 独立API切换功能:
# - 小说正文生成 (improved_reasoning.py) 使用 novel_api_type
# - 小说设定生成 (generate_story_setting.py) 使用 setting_api_type
# - 可以设置为不同的API类型，实现独立控制

# 小说正文生成API (improved_reasoning.py)
novel_api_type: "openai"

# 小说设定生成API (generate_story_setting.py)  
setting_api_type: "ark"

# 💡 切换示例:
# - 如果想让设定生成用ARK，正文生成用OpenAI: setting_api_type: "ark", novel_api_type: "openai"
# - 如果想全部用ARK: 两个都设置为 "ark"
# - 如果想全部用OpenAI: 两个都设置为 "openai"

# API配置 (ARK API for generate_story_setting.py)
api:
  key: "84ef5496-d26f-45c9-90b9-1ba5f4bd18fa"  # 火山引擎ARK API密钥
  model: "ep-20250716165426-p9dr5"              # 使用的模型ID Doubao-1.5-pro-256k
  # model: "ep-20250807145930-2mvq8"              # 使用的模型ID Doubao-1.5-pro-256k-flash
  timeout: 2400                                 # API超时时间（秒）

# ARK API配置 (for improved_reasoning.py when using ark)
api-novel:
  key: "84ef5496-d26f-45c9-90b9-1ba5f4bd18fa"  # 火山引擎ARK API密钥
  model: "ep-20250716165426-p9dr5"              # 使用的模型ID Flash
  timeout: 2400                                 # API超时时间（秒）

# OpenAI API配置 (for improved_reasoning.py when using openai)
openaiapi:
  key: "7820fec1142362d562face7599b09e79.YCMlNnnxC1mEwjgy"
  base_url: "https://open.bigmodel.cn/api/paas/v4"
  model: "glm-4.5-air"
  timeout: 2400

# 生成参数
generation:
  temperature: 1.0          # 创造性控制 (0.0-1.0)
  top_p: 0.7               # 采样参数 (0.0-1.0)
  thinking_disabled: true   # 是否禁用thinking模式 (改为false启用thinking)
  show_thinking: false      # 是否在控制台显示thinking内容
  save_thinking: true       # 是否保存thinking内容到文件
  max_tokens: 8000
  # max_tokens 根据API类型自动选择，无需手动设置
  
# 不同API类型的token限制
token_limits:
  ark: 32768      # ARK API 支持更大的token数
  openai: 65536   # OpenAI兼容API的一般限制 (增加以支持更长的章节)

# 续写控制
continue:
  max_continues: 8         # 最大续写次数
  empty_continue_limit: 2  # 连续空内容生成次数限制
  max_incomplete_continues: 8  # 没有完成标识符时的最大续写次数
  min_chars: 14400         # 小说最小字数要求，与template_variables.TOTAL_MIN_CHARS保持一致



# 文件路径配置
files:
  story_source: "story.md"               # 小说设定源文件（包含设定和细纲）
  main_prompt: "prompt.md"               # 主提示词文件
  continue_prompt: "continue_prompt.md"   # 续写提示词文件
  output_file: "generated_novel.txt"     # 输出文件
  thinking_output: "thinking_content.txt" # thinking内容保存文件
  humanization_module: "humanization_module.md"  # 人类化注入模块文件
  story_template: "story_rules_36.md"       # 小说设定模板文件
  writing_techniques: "writing_techniques.md"    # 写作手法指导文件

# 状态标识符
status:
  complete_marker: "[STORY_COMPLETE]"    # 小说完成标识符

# 统计显示控制
statistics:
  show_detailed_continue_stats: 5        # 显示前N次continue的详细token统计
  show_char_stats: true                 # 是否显示字符统计
  show_token_stats: true                # 是否显示token统计

# 模板变量定义 - 用于在提示词模板中替换{{变量名}}格式的占位符
template_variables:
  # 字数控制变量 - 控制每章和总体字数
  MIN_CHARS_PER_CHAPTER: "1200"    # 每章最小字数要求
  MAX_CHARS_PER_CHAPTER: "1500"    # 每章最大字数限制
  TOTAL_CHAPTERS: "36"             # 总章节数
  TOTAL_MIN_CHARS: "14400"         # 总最小字数 = TOTAL_CHAPTERS × MIN_CHARS_PER_CHAPTER (12×1200=14400)
  MAX_TOTAL_CHARS: "18000"         # 总最大字数 = TOTAL_CHAPTERS × MAX_CHARS_PER_CHAPTER (12×1500=18000)
  
  # 章节结构变量 - 用于生成统一的字数描述
  CHAPTER_RANGE: "1200-1500"       # 章节字数范围 = MIN_CHARS_PER_CHAPTER-MAX_CHARS_PER_CHAPTER
  WORD_REQUIREMENT: "每章必须达到1200-1500中文字，绝不能少于1200字"  # 字数要求描述，包含MIN和MAX值
  
  # 章节内部结构字数 - 控制每章内部开头/中间/结尾的字数分配
  CHAPTER_START_CHARS: "400"       # 章节开头部分字数，约占总字数的20% (400/2000)
  CHAPTER_MIDDLE_CHARS_MIN: "560"  # 章节中间部分最小字数，约占总字数的47% (560/1200)
  CHAPTER_MIDDLE_CHARS_MAX: "700"  # 章节中间部分最大字数，约占总字数的47% (700/1500)
  CHAPTER_END_CHARS: "400"         # 章节结尾部分字数，约占总字数的27% (400/1500)
  # 注意：START + MIDDLE_MIN + END = 400+560+240 = 1200 = MIN_CHARS_PER_CHAPTER
  #      START + MIDDLE_MAX + END = 400+700+400 = 1500 = MAX_CHARS_PER_CHAPTER
  
  # 序章特殊字数 - 序章(第0章)比正文章节稍短
  PROLOGUE_RANGE: "1000-1200"      # 序章字数范围，比正文章节短200-300字左右
  PROLOGUE_START: "300"            # 序章开头部分，比正文开头少100字 (400-100=300)
  PROLOGUE_MIDDLE_MIN: "500"       # 序章中间最小字数，比正文中间少60字 (560-60=500)
  PROLOGUE_MIDDLE_MAX: "600"       # 序章中间最大字数，比正文中间少100字 (700-100=600)
  PROLOGUE_END: "300"              # 序章结尾部分，比正文结尾少100字 (400-100=300)
  # 注意：PROLOGUE各部分加起来 = 300+500+300 = 1100 (最小) / 300+600+300 = 1200 (最大)
  
  # 完成标识 - 小说完成时添加的标记
  COMPLETE_MARKER: "[STORY_COMPLETE]"  # 小说完成标识符，用于判断生成是否完成
  
  # 故事内容变量 - 由improved_reasoning.py动态替换，不在此定义具体值
  # STORY_SETTING: 故事设定内容（从story.md中提取）
  # CHAPTER_OUTLINE: 章节细纲内容（从story.md中提取）