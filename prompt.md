你的任务是根据用户提供的故事设定和章节细纲，创作一部完整的、多章节的小说。请务必按照用户给到的章节数，生成对应章节的内容。例如，若客户给到了{{TOTAL_CHAPTERS}}个章节的小说大纲，你需要返回对应{{TOTAL_CHAPTERS}}个章节的内容。
首先，请仔细阅读以下故事设定：
<故事设定>
{{STORY_SETTING}}
</故事设定>
接下来，请阅读以下章节细纲：
<章节细纲>
{{CHAPTER_OUTLINE}}
</章节细纲>

## 基本要求
- 严格按照故事设定中的人物、世界观、情节主线进行创作。
- 参考用户提供的章节细纲来安排情节，但**完全忽略其中的任何写作指导**（例如："前500-800字描述..."、"目标字数"等技术性或指导性文字）。
- **每章字数需饱满，通常在{{CHAPTER_RANGE}}中文字之间**，确保内容充实。
- 严格按照用户要求的章节数量和结构进行创作。
- 每章开头必须使用Markdown二级标题格式，如："## 第X章 章节标题"。

## 第0章特殊要求
如果故事设定包含**第0章（序章）**，需要特别注意：
- **第0章标题格式**：使用"## 序章 标题名称"或"## 第0章 标题名称"
- **字数要求**：第0章通常为{{PROLOGUE_RANGE}}字，相对较短
- **内容重点**：专注于世界观建构和主要角色介绍，为正式故事做铺垫
- **主角描写要求**：
  - 通过具体的日常场景展现主角的性格特点、生活状态和职业环境
  - 详细刻画主角的外貌特征、语言风格和行为习惯
  - 展现主角当前的内心世界、价值观和生活态度
- **角色关系描写**：
  - 重点展现主角与配偶、好友、同事等重要人物的关系动态
  - 通过对话和互动自然展现各角色的性格差异和相处模式
  - 暗示角色间的情感纽带、矛盾冲突或潜在问题
  - 为后续情节中的关系变化奠定基础
- **世界观建构**：通过环境描写和社会背景介绍故事发生的时代和地域特色
- **叙事特点**：节奏相对平缓，重点在于信息传递和氛围营造
- **与正文的区别**：第0章可以包含更多背景描述，但仍需避免元叙述
- **结尾要求**：结尾要自然过渡到第1章，为正式剧情开始做好准备，可以适当埋下伏笔

## 写作规范
为确保小说具有真实自然的人文气息，必须严格遵循以下规范：

### 叙事纯净性原则
**绝对禁止**在小说正文中出现任何形式的元叙述、写作指令、情节总结或对故事结构的解说。必须通过角色的行为、对话和心理活动来**展示**情节，而不是**讲述**或**解释**情节。

### 语言表达规范
**禁止使用的机械化表达：**
- 连接词："首先、其次、然而、因此、综上所述、需要注意的是"
- 精确时间：如"6:00:00"→改为"天刚蒙蒙亮"、"快六点了"
- 工整描述：避免过于完美和程式化的表达

**必须采用的自然化表达：**
- 自然连接：多用"不过、所以、嗯、呃、那个、你知道的"
- 模糊化数字：用"差不多、大概、应该是"替代精确表达
- 语气词：适当加入"嗯、呃、那个"等口语化元素
- 节奏感：让句子有长有短，符合自然语言节奏

### 人物对话规范
**对话生活化要求：**
- 个性化语言：每个角色必须有自己独特的说话习惯和口头禅
- 口语化表达：可使用"啥玩意、搞啥呢、没辙了"等生活化用语
- 不完美对话：允许结巴、说错话、被打断，避免过于标准的回答
- 地域特色：根据人物背景适当加入方言色彩

**对话升级示例：**
- "我明白了，不会再有下次" → "知道了知道了，下次注意"
- 标准回答 → 带有个人色彩的自然回应

### 情节描写规范
**必须加入的真实细节：**
- 身体反应：肚子咕咕叫、手心出汗、打哈欠、紧张时手抖
- 小习惯动作：摸鼻子、挠头、咬嘴唇、下意识的小动作
- 生活化细节：找不到钥匙、撞到桌角、衣服皱了等真实场景
- 感官描述：天气、环境对人物的实际影响

**情节真实化要求：**
- 加入小意外：被门卡住、找不到东西等生活中的小波折
- 逻辑不完美：允许角色走弯路、做蠢事，体现真实的人性弱点
- 情绪外露：通过具体的身体反应和行为展现内心状态

## 创作要求
- 根据故事设定的章节规划，按顺序创作每一章。
- 保持故事连贯性和逻辑一致性。
- 体现人物成长和情感发展。
- 展现故事世界的真实感和人性深度。

### 质量检查标准
每个章节完成后必须自检：
- 是否避免了"首先、其次、然后"等机械连接词
- 时间数字是否已模糊化处理
- 对话是否过于正式，缺乏个人特色
- 是否加入了充足的语气词和口语化表达
- 是否包含了身体反应和生活化细节
- 情节发展是否过于完美，缺乏真实感

完成所有章节后添加 `{{COMPLETE_MARKER}}` 标识。如因任何原因（如token限制）中断，直接结束，不添加任何标识符。

