import os
from datetime import datetime

def save_prompts_quiet(system_prompt, user_prompt, prefix=""):
    """
    静默保存提示词到固定文件名，直接覆盖之前的文件
    
    Args:
        system_prompt: 系统提示词内容
        user_prompt: 用户提示词内容
        prefix: 文件名前缀，用于区分不同的脚本
    
    Returns:
        tuple: (system_filename, user_filename) 或 (None, None) 如果出错
    """
    try:
        # 创建prompts目录
        prompts_dir = "prompts"
        if not os.path.exists(prompts_dir):
            os.makedirs(prompts_dir)
        
        # 生成固定文件名（不带时间戳，直接覆盖）
        system_filename = f"{prompts_dir}/{prefix}_system.md"
        user_filename = f"{prompts_dir}/{prefix}_user.md"
        
        # 保存system提示词（纯净内容，无元数据）
        with open(system_filename, 'w', encoding='utf-8') as f:
            f.write(system_prompt)
        
        # 保存user提示词（纯净内容，无元数据）
        with open(user_filename, 'w', encoding='utf-8') as f:
            f.write(user_prompt)
        
        return system_filename, user_filename
        
    except Exception as e:
        # 静默处理错误，不影响主程序
        print(f"保存提示词文件时出错: {e}")
        return None, None