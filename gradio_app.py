import gradio as gr
import time
import yaml
import os
from generate_story_setting import generate_story_setting
from improved_reasoning import generate_novel_with_reasoning, count_chinese_chars, is_story_complete
from improved_reasoning import continue_story_generation, find_last_complete_chapter, clean_status_indicators, get_max_tokens
from novel_ideas import get_random_novel_idea

# 从improved_reasoning导入配置加载函数
from improved_reasoning import load_config

def load_application_config():
    """加载应用程序配置文件"""
    with open("application.yaml", 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)

CONFIG = load_config()  # 使用improved_reasoning的load_config函数
APP_CONFIG = load_application_config()

# 简化调试输出
if 'writing_elements' in APP_CONFIG:
    print(f"✅ 成功加载 {len(APP_CONFIG['writing_elements'])} 个写作元素分类")
else:
    print("❌ 错误: 未找到writing_elements配置")

def get_category_choices():
    """获取写作元素分类选择列表"""
    if 'writing_elements' not in APP_CONFIG:
        return []
    
    choices = []
    for category_key, category_data in APP_CONFIG['writing_elements'].items():
        category_name = category_data.get('category', category_key)
        choices.append((category_key, category_name))
    
    return choices

def get_writing_elements_by_category(category):
    """根据分类获取写作元素列表"""
    if not category or 'writing_elements' not in APP_CONFIG:
        return []
    
    if category not in APP_CONFIG['writing_elements']:
        return []
    
    elements = APP_CONFIG['writing_elements'][category].get('elements', {})
    choices = []
    for element_key, element_data in elements.items():
        element_name = element_data.get('name', element_key)
        choices.append((element_key, element_name))
    return choices

def get_writing_elements_by_novel_type(novel_type):
    """根据小说类型(bind字段)获取相关的写作元素"""
    if not novel_type or 'writing_elements' not in APP_CONFIG:
        return []
    
    all_elements = []
    
    # 遍历所有分类和元素，找到匹配bind字段的元素
    for category_key, category_data in APP_CONFIG['writing_elements'].items():
        elements = category_data.get('elements', {})
        for element_key, element_data in elements.items():
            if element_data.get('bind') == novel_type:
                element_name = element_data.get('name', element_key)
                # 返回格式: (element_key, element_name, category_key)
                all_elements.append((element_key, element_name, category_key))
    
    return all_elements

def get_element_description_by_key(element_key):
    """根据element_key获取写作元素的详细描述（跨所有分类搜索）"""
    if not element_key:
        return "选择写作元素后会显示详细说明..."
    
    try:
        if 'writing_elements' not in APP_CONFIG:
            return "配置文件加载失败"
        
        # 在所有分类中查找该元素
        for category_key, category_data in APP_CONFIG['writing_elements'].items():
            elements = category_data.get('elements', {})
            if element_key in elements:
                element_data = elements[element_key]
                element_name = element_data.get('name', element_key)
                description = element_data.get('description', '暂无描述')
                bind_type = element_data.get('bind', '未指定')
                category_name = category_data.get('category', category_key)
                
                return f"**{element_name}**\n\n**所属分类:** {category_name}\n\n**绑定类型:** {bind_type}\n\n**详细描述:**\n{description}"
        
        return "未找到指定写作元素"
    except Exception as e:
        return f"加载元素描述失败: {str(e)}"

def get_element_description(element_key, category):
    """获取写作元素的详细描述（保留原函数兼容性）"""
    if not category or not element_key:
        return "选择写作元素后会显示详细说明..."
    
    try:
        if 'writing_elements' not in APP_CONFIG:
            return "配置文件加载失败"
            
        if category not in APP_CONFIG['writing_elements']:
            return "未找到指定分类"
            
        elements = APP_CONFIG['writing_elements'][category].get('elements', {})
        if element_key not in elements:
            return "未找到指定写作元素"
            
        element_data = elements[element_key]
        element_name = element_data.get('name', element_key)
        description = element_data.get('description', '暂无描述')
        bind_type = element_data.get('bind', '未指定')
        
        return f"**{element_name}**\n\n**绑定类型:** {bind_type}\n\n**详细描述:**\n{description}"
    except Exception as e:
        return f"加载元素描述失败: {str(e)}"

def generate_setting_stream(user_prompt, enable_thinking=False, novel_genre="urban", writing_element=None, protagonists=None, template_choice="36章"):
    """流式生成设定并实时返回更新"""
    if not user_prompt.strip():
        yield "请输入您的小说创意描述", ""
        return
    
    try:
        # 临时修改thinking配置
        original_thinking = CONFIG['generation']['thinking_disabled']
        CONFIG['generation']['thinking_disabled'] = not enable_thinking
        
        # 调用设定生成（传递配置信息，包括模板选择）
        resp = generate_story_setting(user_prompt, novel_genre, writing_element, protagonists, template_choice)
        
        story_setting = ""
        thinking_content = ""
        usage_info = None
        chunk_count = 0
        
        # 初始状态
        yield "🚀 开始生成小说设定...\n", "状态: 初始化中..."
        
        for chunk in resp:
            chunk_count += 1
            
            # 处理thinking内容
            if enable_thinking and chunk.choices and hasattr(chunk.choices[0].delta, 'thinking') and chunk.choices[0].delta.thinking:
                thinking_content += chunk.choices[0].delta.thinking
                # 实时更新thinking显示
                current_output = f"🧠 **推理过程：**\n\n{thinking_content}\n\n---\n\n📝 **生成内容：**\n\n{story_setting}"
                stats = f"生成中... \n推理token数: {len(thinking_content.split())} \n内容token数: {len(story_setting.split())}\n总chunks: {chunk_count}"
                yield current_output, stats
            
            # 处理内容块
            if chunk.choices:
                content = chunk.choices[0].delta.content
                if content:
                    story_setting += content
                    # 实时更新内容显示
                    if enable_thinking:
                        current_output = f"🧠 **推理过程：**\n\n{thinking_content}\n\n---\n\n📝 **生成内容：**\n\n{story_setting}"
                    else:
                        current_output = story_setting
                    
                    stats = f"生成中... \n字符数: {len(story_setting)}\n总chunks: {chunk_count}"
                    if thinking_content:
                        stats += f"\n推理字符数: {len(thinking_content)}"
                    
                    yield current_output, stats
            
            # 捕获使用信息
            if hasattr(chunk, 'usage') and chunk.usage:
                usage_info = chunk.usage
        
        # 保存到story.md文件
        with open('story.md', 'w', encoding='utf-8') as f:
            f.write(story_setting)
        
        # 恢复原始thinking配置
        CONFIG['generation']['thinking_disabled'] = original_thinking
        
        # 准备最终统计信息
        final_stats = f"✅ 生成完成！\n字符数: {len(story_setting)}\n总chunks: {chunk_count}"
        if usage_info:
            final_stats += f"\nToken使用: 输入{usage_info.prompt_tokens} + 输出{usage_info.completion_tokens} = 总计{usage_info.total_tokens}"
        
        # 最终输出
        final_output = story_setting
        if enable_thinking and thinking_content:
            final_output = f"🧠 **推理过程：**\n\n{thinking_content}\n\n---\n\n📝 **生成内容：**\n\n{story_setting}"
        
        # 移除滚动相关代码
        
        yield final_output, final_stats
        
    except Exception as e:
        # 恢复原始thinking配置
        CONFIG['generation']['thinking_disabled'] = original_thinking
        yield f"生成失败: {str(e)}", f"错误: {str(e)}"

def generate_novel_stream(use_existing_setting=True, custom_setting=""):
    """流式生成小说并实时返回更新 - 使用improved_reasoning.py的核心逻辑但保持真正的流式输出"""
    
    try:
        # 检查是否使用现有设定
        if use_existing_setting:
            if not os.path.exists('story.md'):
                yield "错误：未找到story.md文件，请先生成小说设定", ""
                return
            story_source = 'story.md'
        else:
            if not custom_setting.strip():
                yield "错误：请输入自定义设定内容", ""
                return
            # 将自定义设定保存为临时文件
            story_source = 'temp_story.md'
            with open(story_source, 'w', encoding='utf-8') as f:
                f.write(custom_setting)
        
        # 更新配置中的故事源文件
        CONFIG['files']['story_source'] = story_source
        
        yield "🚀 开始生成小说正文...\n", "状态: 初始化中..."
        
        # 初始生成 - 使用improved_reasoning的逻辑但保持流式输出
        resp = generate_novel_with_reasoning()
        
        novel_content = ""
        total_usage_info = []
        chunk_count = 0
        
        yield "📝 正在生成小说内容...\n", "状态: 开始生成..."
        
        # 处理初始生成流 - 实时显示每个chunk
        for chunk in resp:
            chunk_count += 1
            
            # 处理内容块
            if chunk.choices and len(chunk.choices) > 0:
                delta_content = chunk.choices[0].delta.content
                if delta_content:
                    novel_content += delta_content
                    
                    # 实时更新显示
                    stats = f"正在生成... \n字符数: {len(novel_content)}\n中文字数: {count_chinese_chars(novel_content)}\n总chunks: {chunk_count}"
                    yield novel_content, stats
            
            # 捕获使用信息
            if hasattr(chunk, 'usage') and chunk.usage:
                total_usage_info.append(chunk.usage)
        
        # Continue逻辑 - 使用improved_reasoning.py的完全相同的逻辑
        continue_count = 0
        incomplete_continue_count = 0
        empty_count = 0
        max_continues = CONFIG['continue']['max_continues']
        max_incomplete_continues = CONFIG['continue']['max_incomplete_continues'] 
        empty_limit = CONFIG['continue']['empty_continue_limit']
        
        while not is_story_complete(novel_content) and continue_count < max_continues:
            continue_count += 1
            
            # 如果小说未完成（没有完成标识符），增加专门的计数
            if not is_story_complete(novel_content):
                incomplete_continue_count += 1
                # 检查是否超过未完成续写限制
                if incomplete_continue_count > max_incomplete_continues:
                    warning_msg = f"⚠️ 已达到未完成续写限制({max_incomplete_continues}次)，停止Continue"
                    yield novel_content, f"{warning_msg}\n建议检查prompt.md或continue_prompt.md中的完成标识符设置"
                    break
            
            # 显示续写进度信息
            yield novel_content, f"🔄 === Continue 第{continue_count}次 ===\n字符数: {len(novel_content)}\n续写次数: {continue_count}/{max_continues}"
            
            # 获取上次生成的token数
            last_tokens = total_usage_info[-1].completion_tokens if total_usage_info else None
            
            # 调用续写生成 - 使用improved_reasoning的方法
            clean_content, next_chapter = find_last_complete_chapter(novel_content, last_tokens)
            resp = generate_novel_with_reasoning(is_continue=True, existing_content=clean_content, next_chapter=next_chapter)
            
            continued_content = ""
            continue_chunk_count = 0
            
            # 流式处理续写内容 - 实时显示每个chunk
            for chunk in resp:
                continue_chunk_count += 1
                
                # 处理内容块
                if chunk.choices and len(chunk.choices) > 0:
                    delta_content = chunk.choices[0].delta.content
                    if delta_content:
                        continued_content += delta_content
                        
                        # 实时更新续写显示
                        current_total = clean_status_indicators(novel_content) + "\n\n" + continued_content
                        stats = f"续写 #{continue_count} 进行中...\n总字符数: {len(current_total)}\n续写字符数: {len(continued_content)}\n续写chunks: {continue_chunk_count}"
                        yield current_total, stats
                
                # 捕获使用信息
                if hasattr(chunk, 'usage') and chunk.usage:
                    usage_info = chunk.usage
            
            # 将新内容添加到总内容中 - 使用improved_reasoning的完全相同的逻辑
            max_tokens = get_max_tokens()
            was_truncated = last_tokens is not None and last_tokens >= max_tokens
            
            if was_truncated:
                # 如果被截断，需要删除不完整章节再拼接
                clean_base_content, _ = find_last_complete_chapter(novel_content, last_tokens)
                novel_content = clean_base_content + "\n\n" + continued_content
            else:
                # 如果没被截断，直接拼接（只清理状态标识符）
                novel_content = clean_status_indicators(novel_content) + "\n\n" + continued_content
            
            if usage_info:
                total_usage_info.append(usage_info)
            
            # 实时更新续写内容
            stats = f"续写 #{continue_count} 完成\n字符数: {len(novel_content)}\n中文字数: {count_chinese_chars(novel_content)}\n续写次数: {continue_count}"
            yield novel_content, stats
            
            # 检查是否有实际内容生成，防止无限循环
            if not continued_content.strip():
                empty_count += 1
                warning_msg = f"警告: Continue第{continue_count}次没有生成新内容，连续空内容{empty_count}次"
                current_stats = f"{stats}\n\n⚠️ {warning_msg}"
                yield novel_content, current_stats
                if empty_count >= empty_limit:
                    final_warning = f"检测到连续{empty_limit}次无内容生成，停止Continue"
                    yield novel_content, f"{current_stats}\n❌ {final_warning}"
                    break
            else:
                empty_count = 0  # 重置连续空内容计数
        
        # 最终清理和保存 - 使用improved_reasoning的完全相同的逻辑
        last_tokens = total_usage_info[-1].completion_tokens if total_usage_info else None
        final_content, _ = find_last_complete_chapter(novel_content, last_tokens)
        
        with open(CONFIG['files']['output_file'], 'w', encoding='utf-8') as f:
            f.write(final_content)
        
        # 统计信息 - 使用improved_reasoning的完全相同的逻辑
        chinese_char_count = count_chinese_chars(final_content)
        total_char_count = len(final_content)
        word_count = len(final_content.split())
        
        # 状态检查 - 使用improved_reasoning的完全相同的逻辑
        story_completed = is_story_complete(novel_content)
        
        status_checks = [
            (story_completed, "✅ 小说已完整生成"),
            (not story_completed, "⚠️ 小说可能未完整结束"),
            (continue_count >= max_continues, f"⚠️ 达到最大Continue次数限制({max_continues}次)，任务可能未完成"),
            (incomplete_continue_count >= max_incomplete_continues, f"⚠️ 达到未完成续写限制({max_incomplete_continues}次)，小说可能未完整结束"),
        ]
        
        status_message = "⚠️ 小说可能仍未完成"
        for condition, message in status_checks:
            if condition:
                status_message = message
                break
        
        # 准备最终统计信息
        final_stats = f"""=== 任务完成统计 ===
中文字数: {chinese_char_count} 字
总字符数: {total_char_count} 字符（含空格换行）
词语数量: {word_count} 个
Continue 次数: {continue_count}
未完成续写次数: {incomplete_continue_count}

{status_message}"""

        # 统计总的token使用情况
        if total_usage_info:
            total_prompt_tokens = sum(info.prompt_tokens for info in total_usage_info)
            total_completion_tokens = sum(info.completion_tokens for info in total_usage_info)
            total_tokens = sum(info.total_tokens for info in total_usage_info)
            
            final_stats += f"""

=== 总 Token 使用统计 ===
输入 tokens: {total_prompt_tokens}
输出 tokens: {total_completion_tokens}
总计 tokens: {total_tokens}
  初始生成: {total_usage_info[0].completion_tokens} tokens"""
            
            if continue_count > 0:
                avg_continue_tokens = sum(info.completion_tokens for info in total_usage_info[1:]) / continue_count
                final_stats += f"""
  平均每次Continue: {avg_continue_tokens:.0f} tokens"""
                
                # 根据配置显示详细的continue统计
                detail_limit = 5
                if continue_count <= detail_limit:
                    for i in range(1, min(detail_limit + 1, len(total_usage_info))):
                        final_stats += f"""
  Continue {i}: {total_usage_info[i].completion_tokens} tokens"""
        
        # 清理临时文件
        if not use_existing_setting and os.path.exists('temp_story.md'):
            os.remove('temp_story.md')
        
        yield final_content, final_stats
        
    except Exception as e:
        yield f"生成失败: {str(e)}", f"错误: {str(e)}"

def load_existing_setting():
    """加载现有的设定文件"""
    try:
        if os.path.exists('story.md'):
            with open('story.md', 'r', encoding='utf-8') as f:
                return f.read()
        return "## 📄 未找到设定文件\n\n请先在'生成小说设定'页面创建小说设定文件 (story.md)"
    except Exception as e:
        return f"## ❌ 读取错误\n\n读取设定文件失败: {str(e)}"

def load_generated_novel():
    """加载已生成的小说"""
    try:
        if os.path.exists(CONFIG['files']['output_file']):
            with open(CONFIG['files']['output_file'], 'r', encoding='utf-8') as f:
                content = f.read()
            char_count = count_chinese_chars(content)
            return f"## 📖 已生成小说 (字数: {char_count})\n\n---\n\n{content}"
        return "## 📖 未找到小说文件\n\n请先生成小说正文"
    except Exception as e:
        return f"## ❌ 读取错误\n\n读取小说文件失败: {str(e)}"

# 创建Gradio界面
def create_gradio_app():
    # 添加自定义CSS和JavaScript来支持自动滚动
    # 简洁的CSS样式
    custom_css = """
    .markdown {
        scroll-behavior: smooth;
    }
    """
    
    with gr.Blocks(title="AI小说生成器", theme=gr.themes.Soft(), css=custom_css) as app:
        gr.Markdown("# 🎭 AI小说生成器")
        gr.Markdown("先生成小说设定（摘要+细纲），再生成完整小说正文")
        
        with gr.Tabs():
            # 第一个标签页：生成设定
            with gr.TabItem("📝 生成小说设定"):
                gr.Markdown("### 输入您的小说创意，生成详细的小说设定和章节大纲")
                
                with gr.Row():
                    with gr.Column(scale=3):
                        user_input = gr.Textbox(
                            label="小说创意描述",
                            placeholder="请描述您想要的小说类型、主题、风格等...\n例如：科幻小说，关于AI觉醒的故事",
                            lines=5,
                            max_lines=10
                        )
                        
                        with gr.Row():
                            random_idea_btn = gr.Button("🎲 随机生成创意", size="sm", variant="secondary")
                            clear_input_btn = gr.Button("🗑️ 清空内容", size="sm")
                        
                        thinking_checkbox = gr.Checkbox(
                            label="🧠 显示AI推理过程",
                            value=False,
                            info="启用后可以看到AI的思考过程（消耗更多token）"
                        )
                
                # 写作元素配置
                with gr.Accordion("🎨 写作元素配置", open=True):
                    with gr.Row():
                        # 添加模板选择
                        template_choice = gr.Dropdown(
                            choices=["12章", "24章", "36章"],
                            value="36章",
                            label="📖 章节模板",
                            info="选择小说章节数量模板"
                        )
                        
                        # 从配置文件获取小说类型选项
                        def get_novel_genres():
                            if 'novel_genres' not in APP_CONFIG:
                                return [("urban", "都市")]  # 返回(key, name)格式
                            return [(key, data['name']) for key, data in APP_CONFIG['novel_genres'].items()]
                        
                        # 获取类型选项和映射
                        genre_tuples = get_novel_genres()
                        genre_choices = [name for key, name in genre_tuples]  # 显示名称列表
                        genre_name_to_key = {name: key for key, name in genre_tuples}  # 名称到key映射
                        
                        print(f"DEBUG: 小说类型选项: {genre_choices}")
                        print(f"DEBUG: 名称映射: {genre_name_to_key}")
                        
                        novel_genre = gr.Dropdown(
                            choices=genre_choices,
                            value=genre_choices[0] if genre_choices else "都市",
                            label="📚 小说类型"
                        )
                        
                        # 初始加载默认类型的元素
                        default_genre_name = genre_choices[0] if genre_choices else "都市"
                        default_genre_key = genre_name_to_key.get(default_genre_name, "urban")
                        initial_elements = get_writing_elements_by_novel_type(default_genre_key)
                        
                        # 创建初始元素显示选项（只显示名称）
                        element_display_choices = [elem_name for elem_key, elem_name, _ in initial_elements]
                        
                        print(f"DEBUG: 初始元素选项: {element_display_choices[:3]}")
                        
                        writing_element = gr.Dropdown(
                            choices=element_display_choices,
                            label="✨ 写作元素",
                            info="根据小说类型显示相关的写作元素"
                        )
                        
                    with gr.Accordion("📝 主角信息设定", open=True):
                        gr.Markdown("📝 **主角 1**")
                        with gr.Row():
                            protagonist1_name = gr.Textbox(
                                label="主角姓名",
                                placeholder="请输入主角姓名...",
                                scale=1
                            )
                            protagonist1_gender = gr.Dropdown(
                                choices=["男", "女", "其他"],
                                label="性别",
                                value="男",
                                scale=1
                            )
                        
                        add_second_protagonist = gr.Checkbox(
                            label="➕ 添加第二个主角",
                            value=False
                        )
                        
                        with gr.Group(visible=False) as second_protagonist_group:
                            gr.Markdown("📝 **主角 2**")
                            with gr.Row():
                                protagonist2_name = gr.Textbox(
                                    label="主角姓名",
                                    placeholder="请输入第二个主角姓名...",
                                    scale=1
                                )
                                protagonist2_gender = gr.Dropdown(
                                    choices=["男", "女", "其他"],
                                    label="性别",
                                    value="女",
                                    scale=1
                                )
                    
                    element_description = gr.Markdown(
                        value="选择写作元素后会显示详细说明...",
                        label="📖 元素详细说明"
                    )
                        
                    generate_setting_btn = gr.Button("🎯 生成小说设定", variant="primary", size="lg")
                
                    with gr.Column(scale=1):
                        gr.Markdown("#### 💡 创意提示")
                        gr.Markdown("""
                        **可以包含：**
                        - 小说类型（科幻、奇幻、悬疑等）
                        - 主要情节线索
                        - 人物设定要求  
                        - 故事背景时代
                        - 写作风格偏好
                        
                        **章节模板选择：**
                        - **12章**: 短篇小说，简洁紧凑
                        - **24章**: 中篇小说，情节丰富
                        - **36章**: 长篇小说，内容详尽
                        
                        **系统会自动生成：**
                        - 完整的世界观设定
                        - 人物角色介绍
                        - 详细的章节大纲
                        - 各章节内容规划
                        """)
                
                with gr.Row():
                    with gr.Column(scale=2):
                        setting_output = gr.Markdown(
                            label="生成的小说设定",
                            value="点击'生成小说设定'开始创作...",
                            show_copy_button=True,
                            height=600
                        )
                    
                    with gr.Column(scale=1):
                        setting_stats = gr.Textbox(
                            label="实时生成统计", 
                            lines=8,
                            interactive=False,
                            show_copy_button=True
                        )
                
                # 选项联动更新函数  
                def update_writing_elements_by_genre(genre_name):
                    print(f"DEBUG: 选择的小说类型名称: {genre_name}")
                    
                    # 将名称转换为key
                    genre_key = genre_name_to_key.get(genre_name)
                    print(f"DEBUG: 转换后的key: {genre_key}")
                    
                    if not genre_key:
                        print("DEBUG: 未找到对应key，返回空列表")
                        return gr.Dropdown(choices=[], value=None)
                    
                    elements = get_writing_elements_by_novel_type(genre_key)
                    # 只返回显示名称列表
                    element_names = [elem[1] for elem in elements]  # 只要name
                    print(f"DEBUG: 找到 {len(element_names)} 个元素: {element_names[:3]}")
                    
                    return gr.Dropdown(choices=element_names, value=element_names[0] if element_names else None)
                
                def update_element_description_by_name(element_name):
                    print(f"DEBUG: 选择的写作元素名称: {element_name}")
                    
                    if not element_name:
                        return "选择写作元素后会显示详细说明..."
                    
                    # 直接通过名称搜索所有元素
                    try:
                        if 'writing_elements' not in APP_CONFIG:
                            return "配置文件加载失败"
                        
                        for category_key, category_data in APP_CONFIG['writing_elements'].items():
                            elements = category_data.get('elements', {})
                            for element_key, element_data in elements.items():
                                if element_data.get('name') == element_name:
                                    print(f"DEBUG: 找到匹配元素key: {element_key}")
                                    return get_element_description_by_key(element_key)
                        
                        return f"未找到元素: {element_name}"
                    except Exception as e:
                        print(f"DEBUG: 获取元素描述时出错: {e}")
                        return f"获取元素描述失败: {str(e)}"
                
                # 当小说类型改变时，更新写作元素列表
                novel_genre.change(
                    fn=update_writing_elements_by_genre,
                    inputs=[novel_genre],
                    outputs=[writing_element]
                )
                
                # 当写作元素改变时，更新详细说明
                writing_element.change(
                    fn=update_element_description_by_name,
                    inputs=[writing_element],
                    outputs=[element_description]
                )
                
                # 当复选框改变时，显示/隐藏第二个主角
                def toggle_second_protagonist(show_second):
                    return gr.update(visible=show_second)
                
                add_second_protagonist.change(
                    fn=toggle_second_protagonist,
                    inputs=[add_second_protagonist],
                    outputs=[second_protagonist_group]
                )
                
                # 包装函数：将UI显示名称转换为内部key，然后调用实际生成函数
                def generate_setting_wrapper(user_prompt, enable_thinking, template_choice, novel_genre_name, writing_element_name, protagonist1_name, protagonist1_gender, add_second, protagonist2_name, protagonist2_gender):
                    # 转换小说类型名称为key
                    novel_genre_key = genre_name_to_key.get(novel_genre_name, "urban")
                    
                    # 转换写作元素名称为key
                    writing_element_key = None
                    if writing_element_name:
                        for category_key, category_data in APP_CONFIG['writing_elements'].items():
                            elements = category_data.get('elements', {})
                            for element_key, element_data in elements.items():
                                if element_data.get('name') == writing_element_name:
                                    writing_element_key = element_key
                                    break
                            if writing_element_key:
                                break
                    
                    print(f"DEBUG: 转换结果 - 模板: {template_choice}, 类型: {novel_genre_name} -> {novel_genre_key}, 元素: {writing_element_name} -> {writing_element_key}")
                    
                    # 处理主角信息
                    protagonists = []
                    if protagonist1_name and protagonist1_name.strip():
                        protagonists.append({"name": protagonist1_name.strip(), "gender": protagonist1_gender})
                    if add_second and protagonist2_name and protagonist2_name.strip():
                        protagonists.append({"name": protagonist2_name.strip(), "gender": protagonist2_gender})
                    
                    print(f"DEBUG: 主角信息: {protagonists}")
                    
                    # 调用实际生成函数并返回生成器（包括模板选择）
                    yield from generate_setting_stream(user_prompt, enable_thinking, novel_genre_key, writing_element_key, protagonists, template_choice)
                    
                # 随机创意生成函数
                def generate_random_idea():
                    return get_random_novel_idea()
                
                # 清空输入函数
                def clear_input():
                    return ""
                    
                # 绑定随机创意按钮
                random_idea_btn.click(
                    fn=generate_random_idea,
                    outputs=[user_input]
                )
                
                # 绑定清空按钮
                clear_input_btn.click(
                    fn=clear_input,
                    outputs=[user_input]
                )
                    
                # 绑定设定生成事件（流式输出）
                generate_setting_btn.click(
                    fn=generate_setting_wrapper,
                    inputs=[user_input, thinking_checkbox, template_choice, novel_genre, writing_element, protagonist1_name, protagonist1_gender, add_second_protagonist, protagonist2_name, protagonist2_gender],
                    outputs=[setting_output, setting_stats],
                    show_progress=False  # 禁用默认进度条
                )
            
            # 第二个标签页：生成小说
            with gr.TabItem("📖 生成小说正文"):
                gr.Markdown("### 基于设定生成完整的小说正文")
                
                with gr.Row():
                    with gr.Column():
                        use_existing = gr.Radio(
                            choices=[
                                ("使用刚才生成的设定文件 (story.md)", True),
                                ("使用自定义设定", False)
                            ],
                            value=True,
                            label="设定来源"
                        )
                        
                        custom_setting_input = gr.Textbox(
                            label="自定义设定内容",
                            placeholder="如果选择自定义设定，请在此输入完整的小说设定...",
                            lines=10,
                            visible=False
                        )
                        
                        def toggle_custom_setting(choice):
                            return gr.update(visible=not choice)
                        
                        use_existing.change(
                            fn=toggle_custom_setting,
                            inputs=[use_existing],
                            outputs=[custom_setting_input]
                        )
                        
                        generate_novel_btn = gr.Button("📚 开始生成小说", variant="primary", size="lg")
                
                with gr.Row():
                    load_setting_btn = gr.Button("📄 查看当前设定文件", size="sm")
                    load_novel_btn = gr.Button("📖 查看已生成小说", size="sm")
                
                with gr.Row():
                    with gr.Column(scale=2):
                        novel_output = gr.Markdown(
                            label="生成的小说正文",
                            value="选择设定来源并点击'开始生成小说'开始创作...",
                            show_copy_button=True,
                            height=700
                        )
                    
                    with gr.Column(scale=1):
                        novel_stats = gr.Textbox(
                            label="实时生成统计",
                            lines=15,
                            interactive=False,
                            show_copy_button=True
                        )
                
                # 绑定小说生成事件（流式输出）
                generate_novel_btn.click(
                    fn=generate_novel_stream,
                    inputs=[use_existing, custom_setting_input],
                    outputs=[novel_output, novel_stats],
                    show_progress=False  # 禁用默认进度条
                )
                
                # 绑定查看文件事件
                load_setting_btn.click(
                    fn=load_existing_setting,
                    outputs=[novel_output]
                )
                
                load_novel_btn.click(
                    fn=load_generated_novel,
                    outputs=[novel_output]
                )
            
            # 第三个标签页：帮助说明
            with gr.TabItem("ℹ️ 使用说明"):
                gr.Markdown("""
                ## 🚀 使用流程
                
                ### 第一步：生成小说设定
                1. 在"生成小说设定"标签页输入您的创意
                2. 点击"生成小说设定"按钮
                3. 等待系统生成完整的小说设定和章节大纲
                4. 设定会自动保存为 `story.md` 文件
                
                ### 第二步：生成小说正文
                1. 切换到"生成小说正文"标签页
                2. 选择使用刚才生成的设定文件
                3. 点击"开始生成小说"按钮
                4. 系统会自动进行多轮续写直到完成
                5. 完成的小说会保存为 `generated_novel.txt` 文件
                
                ## ⚙️ 系统特性
                
                - **智能续写**：自动检测小说是否完成，需要时进行续写
                - **章节管理**：自动处理章节结构和字数控制
                - **进度显示**：实时显示生成进度和统计信息
                - **文件管理**：自动保存和加载相关文件
                
                ## 📁 生成的文件
                
                - `story.md` - 小说设定和章节大纲
                - `generated_novel.txt` - 完整的小说正文
                - `prompts/` - 生成过程中使用的提示词记录
                
                ## 🎯 配置说明
                
                系统配置文件为 `config.yaml`，包含：
                - API配置（火山引擎ARK + ModelVerse）
                - 生成参数（温度、最大tokens等）
                - 续写控制（最大续写次数等）
                - 文件路径配置
                """)
        
        return app

if __name__ == "__main__":
    app = create_gradio_app()
    app.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        show_error=True,
        debug=True
    )