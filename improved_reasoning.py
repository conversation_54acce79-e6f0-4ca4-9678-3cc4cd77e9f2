import re
import yaml
import time
from typing import Tuple
from template_manager import template_manager
from prompt_saver import save_prompts_quiet

# 动态导入 API 库
try:
    from volcenginesdkarkruntime import Ark
    from volcenginesdkarkruntime.types.chat import ChatCompletionStreamOptionsParam
    ARK_AVAILABLE = True
except ImportError:
    print("警告: volcenginesdkarkruntime 库未安装，无法使用 ARK API")
    ARK_AVAILABLE = False

try:
    from openai import OpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    print("警告: openai 库未安装，无法使用 OpenAI API")
    OPENAI_AVAILABLE = False

def split_story_file(story_file_path: str) -> Tuple[str, str]:
    """
    从story.md中分割提取setting和outline
    
    Returns:
        tuple: (setting_content, outline_content)
    """
    with open(story_file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 移除markdown代码块标记
    content = re.sub(r'^```markdown\s*\n|\n```\s*$', '', content, flags=re.MULTILINE)
    
    # 使用正则表达式找到小说细纲的开始位置
    outline_match = re.search(r'^#{2,3} .*小说细纲（\d+章）', content, flags=re.MULTILINE)
    
    if not outline_match:
        raise ValueError("未找到小说细纲部分")
    
    # 分割内容
    setting_content = content[:outline_match.start()].strip()
    outline_content = content[outline_match.start():].strip()
    
    return setting_content, outline_content

def load_config(config_path="config.yaml"):
    """加载配置文件，根据novel_api_type选择对应的API配置"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
            
            # 获取API类型选择，默认为ark
            api_type = config.get('novel_api_type', 'ark')
            
            if api_type == 'ark':
                if 'api-novel' in config:
                    config['api'] = config['api-novel']
                    config['api_type'] = 'ark'
                else:
                    raise ValueError("配置文件中未找到 api-novel 配置")
            elif api_type == 'openai':
                if 'openaiapi' in config:
                    config['api'] = config['openaiapi']
                    config['api_type'] = 'openai'
                else:
                    raise ValueError("配置文件中未找到 openaiapi 配置")
            else:
                raise ValueError(f"不支持的API类型: {api_type}，仅支持 'ark' 或 'openai'")
            
            return config
    except FileNotFoundError:
        print(f"错误: 配置文件 {config_path} 未找到")
        raise
    except Exception as e:
        print(f"配置文件加载失败: {e}")
        raise


# 全局配置
CONFIG = load_config()

def get_max_tokens():
    """获取当前API类型对应的max_tokens值"""
    api_type = CONFIG.get('api_type', 'ark')
    return CONFIG.get('token_limits', {}).get(api_type, 12288)

# 显示当前使用的API类型
api_type = CONFIG.get('api_type', 'ark')
max_tokens = get_max_tokens()
print(f"[INFO] 小说正文生成API类型: {api_type.upper()}")
print(f"[INFO] 正文生成模型: {CONFIG['api']['model']}")
print(f"[INFO] 正文生成Max Tokens: {max_tokens}")
if CONFIG.get('api_type') == 'openai':
    print(f"[INFO] 正文生成基础URL: {CONFIG['api']['base_url']}")
print()

def load_humanization_module():
    """加载人类化模块内容"""
    try:
        with open(CONFIG['files']['humanization_module'], 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        print("警告: 人类化模块文件未找到，将使用默认设置")
        return ""

def inject_humanization(system_prompt, humanization_content):
    """将人类化模块内容注入到系统提示中"""
    if humanization_content:
        return f"{humanization_content}\n\n{system_prompt}"
    return system_prompt

def process_stream(resp, show_progress=True, collect_usage=True, show_timing=False, start_time=None, show_thinking=None):
    """处理流式响应，返回内容、thinking内容和使用信息"""
    content = ""
    thinking_content = ""
    usage_info = []
    chunk_count = 0
    first_chunk_received = False
    thinking_started = False
    
    # 从配置获取是否显示thinking，如果参数未指定的话
    if show_thinking is None:
        show_thinking = CONFIG['generation'].get('show_thinking', False)
    
    try:
        for chunk in resp:
            chunk_count += 1
            
            # Show timing for first chunk if requested
            if show_timing and not first_chunk_received and start_time:
                print(f"\n[收到第一个数据块耗时: {time.time() - start_time:.2f}s]")
                first_chunk_received = True
            
            # Handle content chunks
            if chunk.choices and len(chunk.choices) > 0:
                delta = chunk.choices[0].delta
                
                # Handle thinking content - check different possible field names
                thinking_text = None
                
                # Try different thinking field names based on API type
                api_type = CONFIG.get('api_type', 'ark')
                
                if api_type == 'ark':
                    # ARK API thinking fields
                    if hasattr(delta, 'thinking') and delta.thinking:
                        thinking_text = delta.thinking
                elif api_type == 'openai':
                    # OpenAI compatible API thinking fields
                    if hasattr(delta, 'thinking') and delta.thinking:
                        thinking_text = delta.thinking
                    # GLM model uses reasoning_content for thinking
                    elif hasattr(delta, 'reasoning_content') and delta.reasoning_content:
                        thinking_text = delta.reasoning_content
                    # Some OpenAI providers might use different field names
                    elif hasattr(delta, 'reasoning') and delta.reasoning:
                        thinking_text = delta.reasoning
                
                # Process thinking content if found
                if thinking_text:
                    thinking_content += thinking_text
                    if show_thinking and show_progress:
                        # 首次开始thinking时显示标题
                        if not thinking_started:
                            print("\n\n=== 模型思考过程 ===", flush=True)
                            thinking_started = True
                        # 直接显示thinking内容，不加前缀
                        print(thinking_text, end="", flush=True)
                
                # Handle regular content
                if delta.content:
                    # 如果刚从thinking切换到内容，先换行分隔
                    if thinking_started and show_thinking and show_progress:
                        print("\n\n=== 生成内容 ===", flush=True)
                        thinking_started = False
                    
                    if show_progress:
                        print(delta.content, end="", flush=True)
                    content += delta.content
            
            # Capture usage information from chunks
            if collect_usage and hasattr(chunk, 'usage') and chunk.usage:
                usage_info.append(chunk.usage)
                
    except Exception as e:
        print(f"\n[生成错误: {e}]")
        if not content:
            content = f"\n[第{chunk_count}次遇到错误，尝试继续...]\n"
    
    return content, thinking_content, usage_info

def create_client():
    """根据配置创建对应的API客户端"""
    api_type = CONFIG.get('api_type', 'ark')
    
    if api_type == 'ark':
        if not ARK_AVAILABLE:
            raise ImportError("ARK API 库未安装，请安装 volcenginesdkarkruntime")
        return Ark(
            api_key=CONFIG['api']['key'],
            timeout=CONFIG['api']['timeout']
        )
    elif api_type == 'openai':
        if not OPENAI_AVAILABLE:
            raise ImportError("OpenAI 库未安装，请安装 openai")
        return OpenAI(
            api_key=CONFIG['api']['key'],
            base_url=CONFIG['api']['base_url'],
            timeout=CONFIG['api']['timeout']
        )
    else:
        raise ValueError(f"不支持的API类型: {api_type}")

def generate_novel_with_reasoning(is_continue=False, existing_content="", next_chapter=None):
    client = create_client()
    
    # Load humanization module
    humanization_content = load_humanization_module()
    
    # Read system prompt and story content
    if is_continue:
        with open(CONFIG['files']['continue_prompt'], 'r', encoding='utf-8') as f:
            prompt_text = f.read()
            # Replace NEXT_CHAPTER variable if provided
            if next_chapter is not None:
                prompt_text = prompt_text.replace('{{NEXT_CHAPTER}}', str(next_chapter))
            system_prompt = template_manager.replace_template(prompt_text)
        # Inject humanization module into continue prompt
        system_prompt = inject_humanization(system_prompt, humanization_content)
        
        # 在Continue模式下也要替换故事设定和章节大纲变量
        story_setting, story_outline = split_story_file(CONFIG['files']['story_source'])
        # 将故事设定和章节大纲作为模板变量替换
        system_prompt = system_prompt.replace('{{STORY_SETTING}}', story_setting)
        system_prompt = system_prompt.replace('{{CHAPTER_OUTLINE}}', story_outline)
        
        # For continue mode, use existing content as input
        story_content = f"请继续以下小说，从中断处无缝衔接继续叙述：\n\n{existing_content}"
    else:
        # 使用模板替换而非字符串拼接
        with open(CONFIG['files']['main_prompt'], 'r', encoding='utf-8') as f:
            main_prompt = f.read()
        
        # 获取故事设定和章节大纲
        story_setting, story_outline = split_story_file(CONFIG['files']['story_source'])
        
        # 替换主提示词中的故事设定和章节大纲变量
        main_prompt = main_prompt.replace('{{STORY_SETTING}}', story_setting)
        main_prompt = main_prompt.replace('{{CHAPTER_OUTLINE}}', story_outline)
        
        # 使用template_manager处理其他模板变量
        system_prompt = template_manager.replace_template(main_prompt)
        
        # 获取总章节数并添加到故事内容中
        # 如果 template_choice = None，默认使用24章，否则使用配置中的TOTAL_CHAPTERS
        total_chapters = CONFIG.get('template_variables', {}).get('TOTAL_CHAPTERS', '24')
        story_content = f"{humanization_content}\n\n请按照要求生成{total_chapters}章小说的完整内容，中间不允许断开，直到输出完成为止。"
    
    # 保存提示词到文件（静默保存，不影响控制台输出）
    prefix = "novel_continue" if is_continue else "novel_main"
    save_prompts_quiet(system_prompt, story_content, prefix)
    
    # 根据API类型调用不同的接口
    api_type = CONFIG.get('api_type', 'ark')
    
    # 根据API类型选择合适的max_tokens
    max_tokens = get_max_tokens()
    
    if api_type == 'ark':
        # ARK API 调用
        thinking_config = {"type": "disabled"} if CONFIG['generation'].get('thinking_disabled', True) else {}
        
        resp = client.chat.completions.create(
            model=CONFIG['api']['model'],
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": story_content}
            ],
            thinking=thinking_config,
            temperature=CONFIG['generation']['temperature'],
            max_tokens=max_tokens,
            top_p=CONFIG['generation']['top_p'],
            stream=True,
            stream_options=ChatCompletionStreamOptionsParam(
                {"include_usage":True, "chunk_include_usage":True},
                total=True
            ),
        )
    elif api_type == 'openai':
        # OpenAI API 调用
        extra_body = {}
        thinking_disabled = CONFIG['generation'].get('thinking_disabled', True)
        
        if thinking_disabled:
            # 某些提供商可能需要显式禁用thinking
            extra_body["chat_template_kwargs"] = {"enable_thinking": False}
        else:
            # 启用thinking功能，支持GLM等模型
            extra_body["thinking"] = {"type": "enabled"}
            
        resp = client.chat.completions.create(
            model=CONFIG['api']['model'],
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": story_content}
            ],
            temperature=CONFIG['generation']['temperature'],
            max_tokens=max_tokens,
            top_p=CONFIG['generation']['top_p'],
            stream=True,
            stream_options={"include_usage": True},
            extra_body=extra_body,
        )
    else:
        raise ValueError(f"不支持的API类型: {api_type}")
    
    return resp

def is_story_complete(content):
    """检查小说是否完成 - 只检查是否有完成标识符"""
    return CONFIG['status']['complete_marker'] in content

def count_chinese_chars(text):
    """计算中文字符数（汉字、标点符号等，不包括空格和换行）"""
    clean_text = clean_status_indicators(text)
    # 移除空白字符，只计算实际内容字符
    return len(re.sub(r'\s', '', clean_text))


def clean_status_indicators(content):
    """清理状态标识符"""
    marker = CONFIG['status']['complete_marker'].replace('[', r'\[').replace(']', r'\]')
    content = re.sub(marker, '', content)
    return content.strip()

def find_last_complete_chapter(content, last_generation_tokens=None):
    """
    找到最后一个完整的章节，删除不完整章节内容
    
    Args:
        content: 小说内容
        last_generation_tokens: 上次生成的token数量
    
    Returns:
        tuple: (cleaned_content, next_chapter_num)
    """
    # 清理状态标识符
    content = clean_status_indicators(content)
    
    # 查找所有章节标题
    chapter_pattern = r'^## 第(\d+)章\s+(.+?)$'
    chapters = list(re.finditer(chapter_pattern, content, flags=re.MULTILINE))
    
    if not chapters:
        # 没有找到章节标题，返回原内容和第1章
        return content.strip(), 1
    
    # 检查是否因为token限制被截断（改为更严格的判断）
    max_tokens = get_max_tokens()
    # 只有在真正接近max_tokens且生成结束位置不完整时才认为被截断
    was_truncated = (last_generation_tokens is not None and 
                    last_generation_tokens >= max_tokens * 0.99 and
                    not content.strip().endswith(('。', '！', '？', '"', '"')))
    
    if was_truncated:
        # 如果被截断，删除最后一个不完整章节，从下一章继续
        last_chapter = chapters[-1]
        if len(chapters) > 1:
            # 有多个章节，删除最后一个被截断的，从下一章开始
            cleaned_content = content[:last_chapter.start()].strip()
            next_chapter_num = int(last_chapter.group(1))
        else:
            # 只有一个被截断章节，清空内容，从第1章重新开始
            cleaned_content = ""
            next_chapter_num = 1
    else:
        # 没有被截断，保留所有内容，从下一章开始
        last_chapter = chapters[-1]
        cleaned_content = content.strip()
        next_chapter_num = int(last_chapter.group(1)) + 1
    
    return cleaned_content, next_chapter_num

def continue_story_generation(existing_content, show_progress=True, expansion_mode=False, last_generation_tokens=None):
    """继续生成小说"""
    # 找到最后完整章节并确定下一章节号
    clean_content, next_chapter = find_last_complete_chapter(existing_content, last_generation_tokens)
    
    if show_progress:
        if expansion_mode:
            print(f"检测到小说字数不足，启动扩展模式（从第{next_chapter}章开始）...")
        else:
            print(f"检测到小说未完成，启动Continue模式（从第{next_chapter}章开始）...")
    
    # 使用continue模式生成
    resp = generate_novel_with_reasoning(is_continue=True, existing_content=clean_content, next_chapter=next_chapter)
    
    continued_content, thinking_content, usage_info_list = process_stream(resp, show_progress=show_progress)
    usage_info = usage_info_list[-1] if usage_info_list else None
    
    return continued_content, thinking_content, usage_info

def generate_complete_novel_stream(show_progress=True, progress_callback=None):
    """
    完整的小说生成流程，支持流式回调
    
    Args:
        show_progress: 是否显示控制台进度
        progress_callback: 进度回调函数，接收 (content, stats) 参数
    
    Returns:
        tuple: (final_content, total_usage_info, statistics)
    """
    try:
        # 初始生成
        resp = generate_novel_with_reasoning()
        
        if show_progress:
            print("开始生成小说... (API)")
        if progress_callback:
            progress_callback("🚀 开始生成小说...\n", "状态: 初始化中...")
            
        start_time = time.time()
        novel_content, thinking_content, total_usage_info = process_stream(resp, show_progress=show_progress, show_timing=True, start_time=start_time)
        
        # 收集所有thinking内容
        all_thinking_content = ""
        if thinking_content:
            all_thinking_content += f"=== 初始生成 Thinking ===\n{thinking_content}\n\n"
        
        if progress_callback:
            stats = f"初始生成完成\n字符数: {len(novel_content)}\n中文字数: {count_chinese_chars(novel_content)}"
            progress_callback(novel_content, stats)
        
        # 检查是否需要continue（未完成或字数不足）
        continue_count = 0
        incomplete_continue_count = 0  # 专门计数没有完成标识符的续写次数
        empty_count = 0  # 连续空内容计数
        max_continues = CONFIG['continue']['max_continues']
        max_incomplete_continues = CONFIG['continue']['max_incomplete_continues']
        empty_limit = CONFIG['continue']['empty_continue_limit']
        
        while not is_story_complete(novel_content) and continue_count < max_continues:
            continue_count += 1
            
            # 如果小说未完成（没有完成标识符），增加专门的计数
            if not is_story_complete(novel_content):
                incomplete_continue_count += 1
                # 检查是否超过未完成续写限制
                if incomplete_continue_count > max_incomplete_continues:
                    warning_msg = f"⚠️ 已达到未完成续写限制({max_incomplete_continues}次)，停止Continue"
                    if show_progress:
                        print(f"\n{warning_msg}")
                        print("建议检查prompt.md或continue_prompt.md中的完成标识符设置")
                    if progress_callback:
                        progress_callback(novel_content, f"{warning_msg}\n建议检查prompt.md或continue_prompt.md中的完成标识符设置")
                    break
            
            # 显示进度信息
            if show_progress:
                print(f"\n=== Continue 第{continue_count}次 ===")
            if progress_callback:
                progress_callback(novel_content, f"🔄 === Continue 第{continue_count}次 ===\n字符数: {len(novel_content)}\n续写次数: {continue_count}/{max_continues}")
            
            # 继续生成（总是显示内容，但控制详细进度）
            # 获取上次生成的token数
            last_tokens = total_usage_info[-1].completion_tokens if total_usage_info else None
            continued_content, continued_thinking, usage_info = continue_story_generation(
                novel_content, 
                show_progress=show_progress,
                expansion_mode=False,
                last_generation_tokens=last_tokens
            )
            
            # 将新内容添加到总内容中（去除原有的状态标识符）
            # 只有在被截断的情况下才需要截取不完整章节
            max_tokens = get_max_tokens()
            was_truncated = last_tokens is not None and last_tokens >= max_tokens
            
            if was_truncated:
                # 如果被截断，需要删除不完整章节再拼接
                clean_base_content, _ = find_last_complete_chapter(novel_content, last_tokens)
                novel_content = clean_base_content + "\n\n" + continued_content
            else:
                # 如果没被截断，直接拼接（只清理状态标识符）
                novel_content = clean_status_indicators(novel_content) + "\n\n" + continued_content
            
            if usage_info:
                total_usage_info.append(usage_info)
            
            # 收集thinking内容
            if continued_thinking:
                all_thinking_content += f"=== Continue 第{continue_count}次 Thinking ===\n{continued_thinking}\n\n"
            
            # 进度回调
            if progress_callback:
                stats = f"续写 #{continue_count} 完成\n字符数: {len(novel_content)}\n中文字数: {count_chinese_chars(novel_content)}\n续写次数: {continue_count}"
                progress_callback(novel_content, stats)
            
            # 检查是否有实际内容生成，防止无限循环
            if not continued_content.strip():
                empty_count += 1
                warning_msg = f"警告: Continue第{continue_count}次没有生成新内容，连续空内容{empty_count}次"
                if show_progress:
                    print(f"\n[{warning_msg}]")
                if progress_callback:
                    current_stats = f"续写 #{continue_count} 完成\n⚠️ {warning_msg}"
                    progress_callback(novel_content, current_stats)
                if empty_count >= empty_limit:
                    final_warning = f"检测到连续{empty_limit}次无内容生成，停止Continue"
                    if show_progress:
                        print(f"[{final_warning}]")
                    if progress_callback:
                        progress_callback(novel_content, f"{current_stats}\n❌ {final_warning}")
                    break
            else:
                empty_count = 0  # 重置连续空内容计数
        
        # 最终清理状态标识符和不完整章节，并保存到文件
        # 获取最后一次生成的token数进行章节完整性检查
        last_tokens = total_usage_info[-1].completion_tokens if total_usage_info else None
        final_content, _ = find_last_complete_chapter(novel_content, last_tokens)
        with open(CONFIG['files']['output_file'], 'w', encoding='utf-8') as f:
            f.write(final_content)
        
        # 保存thinking内容到文件（如果配置启用）
        if all_thinking_content and CONFIG['generation'].get('save_thinking', False):
            with open(CONFIG['files']['thinking_output'], 'w', encoding='utf-8') as f:
                f.write(all_thinking_content)
        
        # Calculate statistics
        chinese_char_count = count_chinese_chars(final_content)
        total_char_count = len(final_content)  # 包含空格换行的总字符数
        word_count = len(final_content.split())
        
        # 详细的完成状态检查
        story_completed = is_story_complete(novel_content)
        length_sufficient = True  # 不再检查字数要求
        
        # 状态检查和消息映射
        status_checks = [
            (story_completed, "✅ 小说已完整生成"),
            (not story_completed, "⚠️  小说可能未完整结束"),
            (continue_count >= max_continues, f"⚠️  达到最大Continue次数限制({max_continues}次)，任务可能未完成"),
            (incomplete_continue_count >= max_incomplete_continues, f"⚠️  达到未完成续写限制({max_incomplete_continues}次)，小说可能未完整结束"),
        ]
        
        # 找到第一个匹配的状态并使用
        status_message = "⚠️  小说可能仍未完成"  # 默认值
        for condition, message in status_checks:
            if condition:
                status_message = message
                break
        
        # 构建统计信息
        statistics = {
            'chinese_char_count': chinese_char_count,
            'total_char_count': total_char_count,
            'word_count': word_count,
            'continue_count': continue_count,
            'incomplete_continue_count': incomplete_continue_count,
            'status_message': status_message,
            'total_usage_info': total_usage_info
        }
        
        if show_progress:
            print(f"\n\n=== 任务完成统计 ===")
            if CONFIG['statistics']['show_char_stats']:
                print(f"中文字数: {chinese_char_count} 字")
                print(f"总字符数: {total_char_count} 字符（含空格换行）")
                print(f"词语数量: {word_count} 个")
            print(f"Continue 次数: {continue_count}")
            print(f"未完成续写次数: {incomplete_continue_count}")
            print(status_message)
            
            # 统计总的token使用情况
            if total_usage_info and CONFIG['statistics']['show_token_stats']:
                total_prompt_tokens = sum(info.prompt_tokens for info in total_usage_info)
                total_completion_tokens = sum(info.completion_tokens for info in total_usage_info)
                total_tokens = sum(info.total_tokens for info in total_usage_info)
                
                print(f"\n=== 总 Token 使用统计 ===")
                print(f"输入 tokens: {total_prompt_tokens}")
                print(f"输出 tokens: {total_completion_tokens}")
                print(f"总计 tokens: {total_tokens}")
                
                # 显示关键统计信息（根据配置）
                print(f"  初始生成: {total_usage_info[0].completion_tokens} tokens")
                if continue_count > 0:
                    avg_continue_tokens = sum(info.completion_tokens for info in total_usage_info[1:]) / continue_count
                    print(f"  平均每次Continue: {avg_continue_tokens:.0f} tokens")
                    
                    # 根据配置显示详细的continue统计
                    detail_limit = CONFIG['statistics']['show_detailed_continue_stats']
                    if continue_count <= detail_limit:
                        for i in range(1, min(detail_limit + 1, len(total_usage_info))):
                            print(f"  Continue {i}: {total_usage_info[i].completion_tokens} tokens")
            elif not total_usage_info:
                print("No usage information available")
        
        return final_content, total_usage_info, statistics
        
    except Exception as e:
        error_msg = f"Error: {e}"
        if show_progress:
            print(error_msg)
        if progress_callback:
            progress_callback(f"生成失败: {str(e)}", f"错误: {str(e)}")
        raise


if __name__ == "__main__":
    # 直接调用新的流式生成函数
    generate_complete_novel_stream(show_progress=True)