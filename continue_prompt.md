# Continue 续写模式

## 故事背景
请参考以下故事设定：
<故事设定>
{{STORY_SETTING}}
</故事设定>

请参考以下章节细纲：
<章节细纲>
{{CHAPTER_OUTLINE}}
</章节细纲>

## 任务
从第{{NEXT_CHAPTER}}章开始生成小说，直到完成所有{{TOTAL_CHAPTERS}}章。

## 基本要求
- 从中断处直接继续，不重复已有内容
- 保持与前文相同的风格和逻辑
- **{{WORD_REQUIREMENT}}**，严格按照{{TOTAL_CHAPTERS}}章结构
- 每章开头显示"## 第X章 章节标题"
- 内容纯净，无元叙述或技术标识符
- **重要：严格忽略任何写作指导内容（如字数分配、情节安排等指导性文字），只专注于故事本身的创作**
- **绝对禁止**在小说正文中出现任何形式的写作指令、情节描述或元注释。例如，严禁出现“故事发展”、“描绘”、“引入”、“结尾留下悬念”等词语。
- **绝对禁止**输出任何结构化标记，例如 `[开头...]`, `[中间...]`, `[结尾...]`。
- **绝对禁止**包含任何与字数统计或目标相关的元数据，例如 `【目标字数：...】`。
- **核心任务**是生成纯粹的小说叙事内容，而不是对创作过程的描述或规划。

## 完成标识
- 完成所有{{TOTAL_CHAPTERS}}章后添加{{COMPLETE_MARKER}}
- 未完成时直接结束，不添加任何标识符

---
**Continue模式已就绪，等待接收中断的小说内容进行续写。**
