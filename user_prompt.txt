## 任务指令
请根据system消息中提供的章节细纲、故事设定和模板要求，创作一个完整的章节内容。严格按照提供的格式要求输出，包括章节正文和下一章输入数据包。

### 当前章节任务
- **章节标题**: 石碑现
- **章节序号**: 第72章
- **大纲主题**: 大纲 2：艰难前行

### 章节细纲内容
#### 【第72章】：石碑现
- **开端**：在众人努力寻找能量晶体时，一名新加入的幸存者在一个隐蔽角落发现了一块刻满奇怪文字的石碑。夏纪看到石碑后，心中涌起一股莫名的熟悉感，直觉告诉他这块石碑与神秘系统有着密切联系。
- **主要情节**：夏纪暂时放下寻找能量晶体的任务，仔细研究石碑上的文字。然而，这些文字晦涩难懂，神秘系统也无法立刻解读。此时，轨道电梯内突然传来一阵强烈的震动，似乎有什么重大变故即将发生。
- **结尾悬念**：石碑上的文字隐藏着什么秘密？轨道电梯内的震动预示着什么？夏纪能否在变故发生前破解能量屏障，进入神秘区域？
- **要素**：
    - **角色**：夏纪、老霍、部分幸存者、新加入的幸存者
    - **场景**：神秘区域入口附近，刻满奇怪文字的石碑在角落，轨道电梯震动，周围物品晃动
    - **线索**：刻满奇怪文字的石碑、轨道电梯震动

### 具体要求
1. **明确任务**：根据上述章节标题"石碑现"和详细的内容细纲，创作对应章节的完整内容
2. **章节信息识别**：按照提供的第72章细纲要点进行创作
3. **严格按照章节细纲进行创作**：将上述细纲中的每一个要点都完整体现在章节内容中，确保情节发展、角色行为、场景描写完全符合细纲要求
4. **字数控制**：确保章节正文字数在3000-3500字范围内
5. **格式要求**：必须包含※※分隔符和完整的下一章输入数据包
6. **保持连贯性**：确保与前文故事情节、角色设定、世界观设定保持一致
7. **细纲执行度**：严格按照提供的章节细纲中的每个情节点进行展开，不得遗漏或擅自修改重要情节

### 章节创作流程
- **第一步**：明确当前创作任务是"石碑现"（第72章）
- **第二步**：逐条阅读上述提供的详细章节细纲要点
- **第三步**：按照细纲中列出的情节顺序逐步展开内容
- **第四步**：确保细纲中的每个要点都在章节内容中得到充分体现和展开
- **第五步**：在严格遵循细纲的前提下进行适度的细节丰富和文学性描写

---

# 人类化注入模块

### 核心原则：真实感与清晰度并重

**重要调整**：人类化技巧必须服务于故事主线，不得干扰情节推进和信息交代。

### 🚨 语言约束 - 强制执行

**绝对语言要求**：
- **唯一输出语言**：全文必须使用**简体中文**，不得出现任何其他语言
- **严禁中英混写**：禁止在同一句话、段落或章节中混用中英文
- **严禁全英文内容**：除非剧情明确需要（如角色阅读外文文档、翻译场景等），否则严禁出现英文内容
- **人名地名规范**：所有人名、地名必须使用中文，如需外国人名请使用中文音译
- **术语统一**：所有专业术语、概念必须使用中文表达，避免英文术语直接出现

**质量检查要求**：
- 创作完成后必须检查全文，确保没有任何英文字母出现
- 如发现英文内容，必须立即替换为对应的中文表达
- 保持语言的纯净性和一致性，提供沉浸式的中文阅读体验

### 量化执行指令（修订版）

| 指标类别 | 修订要求 |
| :--- | :--- |
| **1. 对话真实性** | 保持角色语言特色，但确保关键信息传达清晰 |
| **2. 细节植入频率** | 每 **2000字** 植入 **【1】处** 真实细节，且不得影响主要情节 |
| **3. 工具箱使用限制** | 仅在不影响关键信息交代的场景中使用，优先保证故事清晰度 |